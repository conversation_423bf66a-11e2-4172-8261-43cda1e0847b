{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/app/contact-us/page.tsx"], "sourcesContent": ["function page() {\r\n  return (\r\n    <>\r\n      {/* Header Image Section */}\r\n      <div className=\"relative h-[300px] md:h-[400px] w-full\">\r\n        <img\r\n          src=\"/photo1.png\"\r\n          alt=\"About IREGO\"\r\n          className=\"object-cover w-full h-full\"\r\n        />\r\n        <div className=\"absolute inset-0 bg-black/50 flex items-center justify-center\">\r\n          <h1 className=\"text-3xl md:text-5xl font-bold text-white\">\r\n            Contact <span className=\"text-orange-500\">Us</span>\r\n          </h1>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Contact Section */}\r\n      <div className=\"bg-white px-4 py-16 sm:px-6 lg:px-8\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          <h1 className=\"text-4xl font-bold text-center mb-16\">Contact Us</h1>\r\n\r\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\r\n            {/* Phone Card */}\r\n            <div className=\"bg-orange-50 rounded-2xl p-6 shadow-md text-center\">\r\n              <div className=\"flex justify-center mb-4\">\r\n                <div className=\"bg-orange-100 p-4 rounded-full\">\r\n                  <img src=\"/document_svgrepo.com.png\" alt=\"Phone Icon\" className=\"h-6 w-6\" />\r\n                </div>\r\n              </div>\r\n              <h2 className=\"font-semibold text-xl mb-2\">Phone</h2>\r\n              <p className=\"text-gray-600 text-sm mb-4\">\r\n                To communicate, please call the following numbers:\r\n              </p>\r\n              <div className=\"text-sm font-semibold text-blue-900 space-y-1\">\r\n                <p>0021894-444-4882</p>\r\n                <p>0021894-444-4883</p>\r\n                <p>0039339-299-9065</p>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Email Card */}\r\n            <div className=\"bg-orange-50 rounded-2xl p-6 shadow-md text-center\">\r\n              <div className=\"flex justify-center mb-4\">\r\n                <div className=\"bg-orange-100 p-4 rounded-full\">\r\n                  <img src=\"/document_svgrepo.com.png\" alt=\"Email Icon\" className=\"h-6 w-6\" />\r\n                </div>\r\n              </div>\r\n              <h2 className=\"font-semibold text-xl mb-2\">Email</h2>\r\n              <p className=\"text-gray-600 text-sm mb-4\">\r\n                To communicate via email, please write to:\r\n              </p>\r\n              <p className=\"text-sm font-semibold text-blue-900\"><EMAIL></p>\r\n            </div>\r\n\r\n            {/* Location Card */}\r\n            <div className=\"bg-orange-50 rounded-2xl p-6 shadow-md text-center\">\r\n              <div className=\"flex justify-center mb-4\">\r\n                <div className=\"bg-orange-100 p-4 rounded-full\">\r\n                  <img src=\"/document_svgrepo.com.png\" alt=\"Location Icon\" className=\"h-6 w-6\" />\r\n                </div>\r\n              </div>\r\n              <h2 className=\"font-semibold text-xl mb-2\">Location</h2>\r\n              <p className=\"text-gray-600 text-sm mb-4\">Tripoli International Fair</p>\r\n              <a\r\n                href=\"https://maps.google.com\"\r\n                className=\"text-sm font-semibold text-blue-900 underline\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n              >\r\n                View on Google Maps\r\n              </a>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Embedded Map */}\r\n          <div className=\"mt-16\">\r\n            <iframe\r\n              src=\"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3248.158870268082!2d13.171799774743023!3d32.89290187364118!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x13a8910cc43103d5%3A0x5ee4f535e83858ef!2sTripoli%20Fairground!5e0!3m2!1sen!2sly!4v1717847502879!5m2!1sen!2sly\"\r\n              width=\"100%\"\r\n              height=\"350\"\r\n              style={{ border: 0 }}\r\n              allowFullScreen={true}\r\n              loading=\"lazy\"\r\n              referrerPolicy=\"no-referrer-when-downgrade\"\r\n              className=\"rounded-xl shadow-lg\"\r\n            ></iframe>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default page;\r\n"], "names": [], "mappings": ";;;;;AAAA,SAAS;IACP,qBACE;;0BAEE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,KAAI;wBACJ,KAAI;wBACJ,WAAU;;;;;;kCAEZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;;gCAA4C;8CAChD,8OAAC;oCAAK,WAAU;8CAAkB;;;;;;;;;;;;;;;;;;;;;;;0BAMhD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAuC;;;;;;sCAErD,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,KAAI;oDAA4B,KAAI;oDAAa,WAAU;;;;;;;;;;;;;;;;sDAGpE,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAG1C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAE;;;;;;8DACH,8OAAC;8DAAE;;;;;;8DACH,8OAAC;8DAAE;;;;;;;;;;;;;;;;;;8CAKP,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,KAAI;oDAA4B,KAAI;oDAAa,WAAU;;;;;;;;;;;;;;;;sDAGpE,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAG1C,8OAAC;4CAAE,WAAU;sDAAsC;;;;;;;;;;;;8CAIrD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,KAAI;oDAA4B,KAAI;oDAAgB,WAAU;;;;;;;;;;;;;;;;sDAGvE,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAC1C,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,QAAO;4CACP,KAAI;sDACL;;;;;;;;;;;;;;;;;;sCAOL,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,KAAI;gCACJ,OAAM;gCACN,QAAO;gCACP,OAAO;oCAAE,QAAQ;gCAAE;gCACnB,iBAAiB;gCACjB,SAAQ;gCACR,gBAAe;gCACf,WAAU;;;;;;;;;;;;;;;;;;;;;;;;AAOxB;uCAEe", "debugId": null}}, {"offset": {"line": 363, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 401, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;AAAqC,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAMhG,8BAA8B;IAI5BI,SAASC;;;;;;;;;;;;AAIX,cAAc,0CAA0C,iBAAA;IAAE,MAAA,kBAAwB;AAAsB,EAAC,IAAA,OAAA;IAAA;IAAA;QAEzG,YAAA;YAAA;YAAA,iCAA4D;gBAC5D,OAAO,KAAA;oBAAMG;oBAAAA,EAAc,IAAIX,mBAAmB;qBAChDY,YAAY;8BACVC,IAAAA,CAAMZ,CAAAA;wBAAAA,KAAUa,GAAAA;4BAAAA,IAAQ;4BAAA;yBAAA;;uBACxBC,MAAM;;iBACNC,UAAU;sBACV,IAAA,CAAA;YAAA;SAAA,gCAA2C;;SAC3CC,YAAY;cACZC,IAAAA;YAAAA,IAAU,CAAA;YAAA;SAAA;cACVC,GAAAA;YAAAA,IAAU,EAAE;YAAA;SAAA;UACd,SAAA;YAAA,IAAA;YAAA;SAAA;UACAC,UAAU,CAAA;YAAA,IAAA;YAAA;SAAA;cACRC,OAAAA;YAAAA,IAAYnB;YAAAA;SAAAA;UACd,cAAA;YAAA,IAAA;YAAA;SAAA;IACF;CAAA,CAAE", "ignoreList": [0], "debugId": null}}]}