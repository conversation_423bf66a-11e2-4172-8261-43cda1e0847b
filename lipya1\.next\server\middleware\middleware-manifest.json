{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|.*\\.png$|.*\\.jpg$|.*\\.jpeg$|.*\\.gif$|.*\\.svg$).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|.*\\.png$|.*\\.jpg$|.*\\.jpeg$|.*\\.gif$|.*\\.svg$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SuOzsvn0SKfTqZAUjO154u/ZZoIpsbZ/90BJU7XLQRw=", "__NEXT_PREVIEW_MODE_ID": "f920326fb495389fbd68efdceaed0613", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "88803fd9139906870eb8c0a8eb52ef2e0aa60cce719fb19a2680bea6bcfb68f1", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3b45926911cf8d2e7aede0204ef7f5d2b46253bff0c1ac84011a222f3341a16c"}}}, "instrumentation": null, "functions": {}}