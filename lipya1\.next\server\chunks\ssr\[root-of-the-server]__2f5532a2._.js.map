{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/context/UserContext.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { createContext, useContext, useState, ReactNode } from \"react\";\r\n\r\ntype User = {\r\n  name?: string;\r\n  email?: string;\r\n  photoURL?: string;\r\n};\r\n\r\ntype UserContextType = {\r\n  user: User | null;\r\n  setUser: (user: User | null) => void;\r\n};\r\n\r\nconst UserContext = createContext<UserContextType | undefined>(undefined);\r\n\r\nexport function UserProvider({ children }: { children: ReactNode }) {\r\n  const [user, setUser] = useState<User | null>(null);\r\n\r\n  return (\r\n    <UserContext.Provider value={{ user, setUser }}>\r\n      {children}\r\n    </UserContext.Provider>\r\n  );\r\n}\r\n\r\nexport function useUser() {\r\n  const context = useContext(UserContext);\r\n  if (!context) {\r\n    throw new Error(\"useUser must be used within a UserProvider\");\r\n  }\r\n  return context;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAeA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAA2B;IAChE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAE9C,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;YAAE;YAAM;QAAQ;kBAC1C;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/components/SafeImage.tsx"], "sourcesContent": ["\"use client\";\n\nimport Image from 'next/image';\nimport { useState } from 'react';\n\ninterface SafeImageProps {\n  src: string;\n  alt: string;\n  width: number;\n  height: number;\n  className?: string;\n  fallbackSrc?: string;\n  priority?: boolean;\n  suppressHydrationWarning?: boolean;\n}\n\nexport default function SafeImage({ \n  src, \n  alt, \n  width, \n  height, \n  className, \n  fallbackSrc = '/logo.png', \n  priority = false,\n  suppressHydrationWarning = false\n}: SafeImageProps) {\n  const [imgSrc, setImgSrc] = useState(src);\n  const [hasError, setHasError] = useState(false);\n\n  const handleError = () => {\n    if (!hasError) {\n      setHasError(true);\n      setImgSrc(fallbackSrc);\n    }\n  };\n\n  return (\n    <Image\n      src={imgSrc}\n      alt={alt}\n      width={width}\n      height={height}\n      className={className}\n      priority={priority}\n      suppressHydrationWarning={suppressHydrationWarning}\n      onError={handleError}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAgBe,SAAS,UAAU,EAChC,GAAG,EACH,GAAG,EACH,KAAK,EACL,MAAM,EACN,SAAS,EACT,cAAc,WAAW,EACzB,WAAW,KAAK,EAChB,2BAA2B,KAAK,EACjB;IACf,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,cAAc;QAClB,IAAI,CAAC,UAAU;YACb,YAAY;YACZ,UAAU;QACZ;IACF;IAEA,qBACE,8OAAC,6HAAA,CAAA,UAAK;QACJ,KAAK;QACL,KAAK;QACL,OAAO;QACP,QAAQ;QACR,WAAW;QACX,UAAU;QACV,0BAA0B;QAC1B,SAAS;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/components/Navbar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport Image from \"next/image\";\r\nimport { Menu, X } from \"lucide-react\";\r\nimport { useUser } from \"../context/UserContext\"; // عدل حسب مكان الملف\r\nimport { useRouter } from \"next/navigation\";\r\nimport SafeImage from \"./SafeImage\";\r\n\r\n function Navbar() {\r\n  const [menuOpen, setMenuOpen] = useState(false);\r\n  const [dropdownOpen, setDropdownOpen] = useState(false);\r\n  const toggleMenu = () => setMenuOpen(!menuOpen);\r\n\r\n  const { user, setUser } = useUser();\r\n  const router = useRouter();\r\n\r\n  const dropdownRef = useRef<HTMLDivElement>(null);\r\n\r\n  // إغلاق الـ dropdown عند الضغط خارجها\r\n  useEffect(() => {\r\n    function handleClickOutside(event: MouseEvent) {\r\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\r\n        setDropdownOpen(false);\r\n      }\r\n    }\r\n    if (dropdownOpen) {\r\n      document.addEventListener(\"mousedown\", handleClickOutside);\r\n    } else {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    }\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, [dropdownOpen]);\r\n\r\n  const handleLogout = () => {\r\n    setUser(null); // مسح بيانات المستخدم (تأكد من تنفيذ هذه الوظيفة في UserContext)\r\n    setDropdownOpen(false);\r\n    router.push(\"/login\"); // توجيه لصفحة تسجيل الدخول\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <nav className=\"absolute top-[35px] left-0 w-full bg-gray-800/30 text-white z-40 backdrop-blur-md border-b border-white/10\">\r\n  <div className=\"max-w-7xl mx-auto px-4 py-3 flex items-center justify-between h-16\">\r\n    {/* الشعار */}\r\n    <div className=\"flex items-center space-x-2 min-w-[60px]\">\r\n      <Image src=\"/logo.png\" alt=\"Logo\" width={60} height={40} priority suppressHydrationWarning />\r\n    </div>\r\n\r\n    {/* روابط سطح المكتب */}\r\n<ul className=\"hidden md:flex items-center gap-3  text-sm uppercase font-medium\">\r\n\r\n  <li>\r\n    <Link href=\"/\" className=\"hover:text-orange-400 transition\">\r\n      Home\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/about\" className=\"hover:text-orange-400 transition\">\r\n      About\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/committees\" className=\"hover:text-orange-400 transition\">\r\n      Committees\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/call-for-papers\" className=\"hover:text-orange-400 transition\">\r\n      Call  Papers\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/submission\" className=\"hover:text-orange-400 transition\">\r\n      Submission\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/registration\" className=\"hover:text-orange-400 transition\">\r\n      Registration\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/venue\" className=\"hover:text-orange-400 transition\">\r\n      Venue\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/expo\" className=\"hover:text-orange-400 transition\">\r\n     Expo\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/the-best\" className=\"hover:text-orange-400 transition\">\r\n        Best\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link\r\n      href=\"/contact-us\"\r\n      scroll={true}\r\n      onClick={toggleMenu}\r\n      className=\"bg-orange-500 hover:bg-orange-600 text-white px-4 py-1.5 rounded-md text-xs font-semibold\"\r\n    >\r\n      Contact \r\n    </Link>\r\n  </li>\r\n</ul>\r\n\r\n\r\n    {/* صورة المستخدم أو زر تسجيل الدخول + زر القائمة للموبايل */}\r\n    <div className=\"flex items-center space-x-3 min-w-[100px] justify-end relative\">\r\n      {user ? (\r\n        <>\r\n          <button onClick={() => setDropdownOpen(!dropdownOpen)} className=\"focus:outline-none\">\r\n            <SafeImage\r\n              src={user.photoURL || \"/logo.png\"}\r\n              alt=\"User\"\r\n              width={36}\r\n              height={36}\r\n              className=\"rounded-full border border-white\"\r\n              fallbackSrc=\"/logo.png\"\r\n              suppressHydrationWarning\r\n            />\r\n          </button>\r\n\r\n          {dropdownOpen && (\r\n            <div\r\n              ref={dropdownRef}\r\n              className=\"absolute right-0 mt-2 w-40 bg-gray-900 border border-gray-700 rounded shadow-lg z-50\"\r\n            >\r\n              <button\r\n                onClick={handleLogout}\r\n                className=\"block w-full text-left px-4 py-2 text-sm hover:bg-orange-600 hover:text-white text-red-500\"\r\n              >\r\n                تسجيل الخروج\r\n              </button>\r\n            </div>\r\n          )}\r\n        </>\r\n      ) : (\r\n        <Link\r\n          href=\"/login\"\r\n          className=\"text-sm bg-orange-500 hover:bg-orange-600 px-3 py-1 rounded text-white\"\r\n        >\r\n          Login\r\n        </Link>\r\n      )}\r\n\r\n      {/* زر القائمة للموبايل */}\r\n      <button\r\n        className=\"md:hidden text-white\"\r\n        onClick={toggleMenu}\r\n        aria-label=\"Toggle menu\"\r\n      >\r\n        {menuOpen ? <X size={28} /> : <Menu size={28} />}\r\n      </button>\r\n    </div>\r\n  </div>\r\n\r\n  {/* القائمة الجانبية للموبايل */}\r\n  {menuOpen && (\r\n    <div className=\"md:hidden flex flex-col px-6 pb-4 pt-2 space-y-4 bg-gray-800/30 text-white backdrop-blur-md border-t border-white/10\">\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/\" onClick={toggleMenu}>Home</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/about\" onClick={toggleMenu}>About us</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/committees\" onClick={toggleMenu}>Committees</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/call-for-papers\" onClick={toggleMenu}>Call For Papers</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/submission\" onClick={toggleMenu}>Submission</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/registration\" onClick={toggleMenu}>Registration</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/venue\" onClick={toggleMenu}>Venue</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/expo\">IREGO Expo</Link>{/* Corrected href */}\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/the-best\">IREGO The Best</Link>\r\n    \r\n      \r\n      <Link\r\n        href=\"/contact-us\"\r\n        scroll={true}\r\n        onClick={toggleMenu}\r\n        className=\"block bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md text-sm font-semibold w-fit\"\r\n      >\r\n        Contact Us\r\n      </Link>\r\n    </div>\r\n  )}\r\n</nav>\r\n    \r\n    </>\r\n  );\r\n}\r\nexport default Navbar ; "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA,8NAAkD,qBAAqB;AACvE;AACA;AARA;;;;;;;;;AAUC,SAAS;IACR,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,aAAa,IAAM,YAAY,CAAC;IAEtC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,mBAAmB,KAAiB;YAC3C,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,gBAAgB;YAClB;QACF;QACA,IAAI,cAAc;YAChB,SAAS,gBAAgB,CAAC,aAAa;QACzC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;QACA,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG;QAAC;KAAa;IAEjB,MAAM,eAAe;QACnB,QAAQ,OAAO,iEAAiE;QAChF,gBAAgB;QAChB,OAAO,IAAI,CAAC,WAAW,2BAA2B;IACpD;IAEA,qBACE;kBACE,cAAA,8OAAC;YAAI,WAAU;;8BACnB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCAAC,KAAI;gCAAY,KAAI;gCAAO,OAAO;gCAAI,QAAQ;gCAAI,QAAQ;gCAAC,wBAAwB;;;;;;;;;;;sCAIhG,8OAAC;4BAAG,WAAU;;8CAEZ,8OAAC;8CACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAmC;;;;;;;;;;;8CAK9D,8OAAC;8CACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAmC;;;;;;;;;;;8CAKnE,8OAAC;8CACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAc,WAAU;kDAAmC;;;;;;;;;;;8CAKxE,8OAAC;8CACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAmB,WAAU;kDAAmC;;;;;;;;;;;8CAK7E,8OAAC;8CACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAc,WAAU;kDAAmC;;;;;;;;;;;8CAKxE,8OAAC;8CACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAgB,WAAU;kDAAmC;;;;;;;;;;;8CAK1E,8OAAC;8CACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAmC;;;;;;;;;;;8CAKnE,8OAAC;8CACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAQ,WAAU;kDAAmC;;;;;;;;;;;8CAKlE,8OAAC;8CACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAAmC;;;;;;;;;;;8CAKtE,8OAAC;8CACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,QAAQ;wCACR,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAQD,8OAAC;4BAAI,WAAU;;gCACZ,qBACC;;sDACE,8OAAC;4CAAO,SAAS,IAAM,gBAAgB,CAAC;4CAAe,WAAU;sDAC/D,cAAA,8OAAC,+HAAA,CAAA,UAAS;gDACR,KAAK,KAAK,QAAQ,IAAI;gDACtB,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;gDACV,aAAY;gDACZ,wBAAwB;;;;;;;;;;;wCAI3B,8BACC,8OAAC;4CACC,KAAK;4CACL,WAAU;sDAEV,cAAA,8OAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;iEAOP,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAMH,8OAAC;oCACC,WAAU;oCACV,SAAS;oCACT,cAAW;8CAEV,yBAAW,8OAAC,4LAAA,CAAA,IAAC;wCAAC,MAAM;;;;;6DAAS,8OAAC,kMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;gBAM/C,0BACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAI,SAAS;sCAAY;;;;;;sCACjF,8OAAC,4JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAS,SAAS;sCAAY;;;;;;sCACtF,8OAAC,4JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAc,SAAS;sCAAY;;;;;;sCAC3F,8OAAC,4JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAmB,SAAS;sCAAY;;;;;;sCAChG,8OAAC,4JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAc,SAAS;sCAAY;;;;;;sCAC3F,8OAAC,4JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAgB,SAAS;sCAAY;;;;;;sCAC7F,8OAAC,4JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAS,SAAS;sCAAY;;;;;;sCACtF,8OAAC,4JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;sCAAQ;;;;;;sCAChE,8OAAC,4JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;sCAAY;;;;;;sCAGpE,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,QAAQ;4BACR,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;AASP;uCACe", "debugId": null}}, {"offset": {"line": 564, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/components/ErrorBoundary.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\n\ninterface ErrorBoundaryState {\n  hasError: boolean;\n  error?: Error;\n}\n\ninterface ErrorBoundaryProps {\n  children: React.ReactNode;\n  fallback?: React.ComponentType<{ error: Error; resetError: () => void }>;\n}\n\nclass ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {\n  constructor(props: ErrorBoundaryProps) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError(error: Error): ErrorBoundaryState {\n    return { hasError: true, error };\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('Error caught by boundary:', error, errorInfo);\n  }\n\n  resetError = () => {\n    this.setState({ hasError: false, error: undefined });\n  };\n\n  render() {\n    if (this.state.hasError) {\n      if (this.props.fallback) {\n        const FallbackComponent = this.props.fallback;\n        return <FallbackComponent error={this.state.error!} resetError={this.resetError} />;\n      }\n\n      return (\n        <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n          <div className=\"max-w-md w-full bg-white rounded-lg shadow-md p-6 text-center\">\n            <div className=\"text-red-500 text-6xl mb-4\">⚠️</div>\n            <h2 className=\"text-2xl font-bold text-gray-800 mb-2\">Something went wrong</h2>\n            <p className=\"text-gray-600 mb-4\">\n              We're sorry, but something unexpected happened. Please try refreshing the page.\n            </p>\n            <button\n              onClick={this.resetError}\n              className=\"bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded font-semibold\"\n            >\n              Try Again\n            </button>\n            <button\n              onClick={() => window.location.reload()}\n              className=\"ml-2 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded font-semibold\"\n            >\n              Refresh Page\n            </button>\n          </div>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nexport default ErrorBoundary;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAcA,MAAM,sBAAsB,qMAAA,CAAA,UAAK,CAAC,SAAS;IACzC,YAAY,KAAyB,CAAE;QACrC,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,yBAAyB,KAAY,EAAsB;QAChE,OAAO;YAAE,UAAU;YAAM;QAAM;IACjC;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,6BAA6B,OAAO;IACpD;IAEA,aAAa;QACX,IAAI,CAAC,QAAQ,CAAC;YAAE,UAAU;YAAO,OAAO;QAAU;IACpD,EAAE;IAEF,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,MAAM,oBAAoB,IAAI,CAAC,KAAK,CAAC,QAAQ;gBAC7C,qBAAO,8OAAC;oBAAkB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;oBAAG,YAAY,IAAI,CAAC,UAAU;;;;;;YACjF;YAEA,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAA6B;;;;;;sCAC5C,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,8OAAC;4BACC,SAAS,IAAI,CAAC,UAAU;4BACxB,WAAU;sCACX;;;;;;sCAGD,8OAAC;4BACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;4BACrC,WAAU;sCACX;;;;;;;;;;;;;;;;;QAMT;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 676, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/components/ChatbotPopup.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from 'react';\r\n\r\nconst ChatbotPopup = () => {\r\n  const [showChat, setShowChat] = useState(false);\r\n\r\n  const toggleChat = () => {\r\n    setShowChat(!showChat);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {/* زر ثابت في الركن السفلي الأيمن */}\r\n      <button\r\n        style={{\r\n          position: 'fixed',\r\n          bottom: '20px',\r\n          right: '20px',\r\n          zIndex: 1000,\r\n          padding: '10px 20px',\r\n     background: '#007bff',\r\n          color: 'white',\r\n          border: 'none',\r\n          borderRadius: '50px',\r\n          cursor: 'pointer',\r\n        }}\r\n        onClick={toggleChat}\r\n      >\r\n        Chat\r\n      </button>\r\n\r\n      {/* الصفحة اللي بتظهر عند الضغط */}\r\n      {showChat && (\r\n        <div\r\n          style={{\r\n            position: 'fixed',\r\n            bottom: '70px',\r\n            right: '20px',\r\n            width: '300px',\r\n            height: '400px',\r\n            backgroundColor: 'white',\r\n            boxShadow: '0 4px 8px rgba(0,0,0,0.2)',\r\n            borderRadius: '10px',\r\n            zIndex: 999,\r\n            display: 'flex',\r\n            flexDirection: 'column',\r\n          }}\r\n        >\r\n          {/* رأس الـ Chatbot */}\r\n          <div\r\n            style={{\r\n              backgroundColor: '#007bff',\r\n              color: 'white',\r\n              padding: '10px',\r\n              borderTopLeftRadius: '10px',\r\n              borderTopRightRadius: '10px',\r\n            }}\r\n          >\r\n            <h4 style={{ margin: 0 }}>محادثة الدعم</h4>\r\n          </div>\r\n          \r\n          {/* محتوى الـ Chatbot */}\r\n          <div style={{ flex: 1, padding: '10px', overflowY: 'auto' }}>\r\n            {/* هنا تضع الـ iframe أو مكون الـ Chatbot الخاص بك */}\r\n            {/* مثال: */}\r\n            <iframe\r\n              src=\"YOUR_CHATBOT_URL\"\r\n              title=\"Chatbot\"\r\n              style={{ width: '100%', height: '100%', border: 'none' }}\r\n            />\r\n          </div>\r\n\r\n          {/* زر إغلاق */}\r\n          <button\r\n            style={{\r\n              position: 'absolute',\r\n              top: '5px',\r\n              right: '10px',\r\n              background: 'transparent',\r\n              border: 'none',\r\n              fontSize: '20px',\r\n              cursor: 'pointer',\r\n            }}\r\n            onClick={toggleChat}\r\n          >\r\n            &times;\r\n          </button>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ChatbotPopup;"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,eAAe;IACnB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,aAAa;QACjB,YAAY,CAAC;IACf;IAEA,qBACE;;0BAEE,8OAAC;gBACC,OAAO;oBACL,UAAU;oBACV,QAAQ;oBACR,OAAO;oBACP,QAAQ;oBACR,SAAS;oBACd,YAAY;oBACP,OAAO;oBACP,QAAQ;oBACR,cAAc;oBACd,QAAQ;gBACV;gBACA,SAAS;0BACV;;;;;;YAKA,0BACC,8OAAC;gBACC,OAAO;oBACL,UAAU;oBACV,QAAQ;oBACR,OAAO;oBACP,OAAO;oBACP,QAAQ;oBACR,iBAAiB;oBACjB,WAAW;oBACX,cAAc;oBACd,QAAQ;oBACR,SAAS;oBACT,eAAe;gBACjB;;kCAGA,8OAAC;wBACC,OAAO;4BACL,iBAAiB;4BACjB,OAAO;4BACP,SAAS;4BACT,qBAAqB;4BACrB,sBAAsB;wBACxB;kCAEA,cAAA,8OAAC;4BAAG,OAAO;gCAAE,QAAQ;4BAAE;sCAAG;;;;;;;;;;;kCAI5B,8OAAC;wBAAI,OAAO;4BAAE,MAAM;4BAAG,SAAS;4BAAQ,WAAW;wBAAO;kCAGxD,cAAA,8OAAC;4BACC,KAAI;4BACJ,OAAM;4BACN,OAAO;gCAAE,OAAO;gCAAQ,QAAQ;gCAAQ,QAAQ;4BAAO;;;;;;;;;;;kCAK3D,8OAAC;wBACC,OAAO;4BACL,UAAU;4BACV,KAAK;4BACL,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,UAAU;4BACV,QAAQ;wBACV;wBACA,SAAS;kCACV;;;;;;;;;;;;;;AAOX;uCAEe", "debugId": null}}]}