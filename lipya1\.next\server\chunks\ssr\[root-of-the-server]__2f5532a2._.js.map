{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/context/UserContext.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { createContext, useContext, useState, ReactNode } from \"react\";\r\n\r\ntype User = {\r\n  name?: string;\r\n  email?: string;\r\n  photoURL?: string;\r\n};\r\n\r\ntype UserContextType = {\r\n  user: User | null;\r\n  setUser: (user: User | null) => void;\r\n};\r\n\r\nconst UserContext = createContext<UserContextType | undefined>(undefined);\r\n\r\nexport function UserProvider({ children }: { children: ReactNode }) {\r\n  const [user, setUser] = useState<User | null>(null);\r\n\r\n  return (\r\n    <UserContext.Provider value={{ user, setUser }}>\r\n      {children}\r\n    </UserContext.Provider>\r\n  );\r\n}\r\n\r\nexport function useUser() {\r\n  const context = useContext(UserContext);\r\n  if (!context) {\r\n    throw new Error(\"useUser must be used within a UserProvider\");\r\n  }\r\n  return context;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAeA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAA2B;IAChE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAE9C,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;YAAE;YAAM;QAAQ;kBAC1C;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/components/SafeImage.tsx"], "sourcesContent": ["\"use client\";\n\nimport Image from 'next/image';\nimport { useState } from 'react';\n\ninterface SafeImageProps {\n  src: string;\n  alt: string;\n  width: number;\n  height: number;\n  className?: string;\n  fallbackSrc?: string;\n  priority?: boolean;\n  suppressHydrationWarning?: boolean;\n}\n\nexport default function SafeImage({ \n  src, \n  alt, \n  width, \n  height, \n  className, \n  fallbackSrc = '/logo.png', \n  priority = false,\n  suppressHydrationWarning = false\n}: SafeImageProps) {\n  const [imgSrc, setImgSrc] = useState(src);\n  const [hasError, setHasError] = useState(false);\n\n  const handleError = () => {\n    if (!hasError) {\n      setHasError(true);\n      setImgSrc(fallbackSrc);\n    }\n  };\n\n  return (\n    <Image\n      src={imgSrc}\n      alt={alt}\n      width={width}\n      height={height}\n      className={className}\n      priority={priority}\n      suppressHydrationWarning={suppressHydrationWarning}\n      onError={handleError}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAgBe,SAAS,UAAU,EAChC,GAAG,EACH,GAAG,EACH,KAAK,EACL,MAAM,EACN,SAAS,EACT,cAAc,WAAW,EACzB,WAAW,KAAK,EAChB,2BAA2B,KAAK,EACjB;IACf,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,cAAc;QAClB,IAAI,CAAC,UAAU;YACb,YAAY;YACZ,UAAU;QACZ;IACF;IAEA,qBACE,8OAAC,6HAAA,CAAA,UAAK;QACJ,KAAK;QACL,KAAK;QACL,OAAO;QACP,QAAQ;QACR,WAAW;QACX,UAAU;QACV,0BAA0B;QAC1B,SAAS;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/components/Navbar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport Image from \"next/image\";\r\nimport { Menu, X } from \"lucide-react\";\r\nimport { useUser } from \"../context/UserContext\"; // عدل حسب مكان الملف\r\nimport { useRouter } from \"next/navigation\";\r\nimport SafeImage from \"./SafeImage\";\r\n\r\n function Navbar() {\r\n  const [menuOpen, setMenuOpen] = useState(false);\r\n  const [dropdownOpen, setDropdownOpen] = useState(false);\r\n  const toggleMenu = () => setMenuOpen(!menuOpen);\r\n\r\n  const { user, setUser } = useUser();\r\n  const router = useRouter();\r\n\r\n  const dropdownRef = useRef<HTMLDivElement>(null);\r\n\r\n  // إغلاق الـ dropdown عند الضغط خارجها\r\n  useEffect(() => {\r\n    function handleClickOutside(event: MouseEvent) {\r\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\r\n        setDropdownOpen(false);\r\n      }\r\n    }\r\n    if (dropdownOpen) {\r\n      document.addEventListener(\"mousedown\", handleClickOutside);\r\n    } else {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    }\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, [dropdownOpen]);\r\n\r\n  const handleLogout = () => {\r\n    setUser(null); // مسح بيانات المستخدم (تأكد من تنفيذ هذه الوظيفة في UserContext)\r\n    setDropdownOpen(false);\r\n    router.push(\"/login\"); // توجيه لصفحة تسجيل الدخول\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <nav className=\"absolute top-[35px] left-0 w-full bg-gray-800/30 text-white z-40 backdrop-blur-md border-b border-white/10\">\r\n  <div className=\"max-w-7xl mx-auto px-4 py-3 flex items-center justify-between h-16\">\r\n    {/* الشعار */}\r\n    <div className=\"flex items-center space-x-2 min-w-[60px]\">\r\n      <Image src=\"/logo.png\" alt=\"Logo\" width={60} height={40} priority suppressHydrationWarning />\r\n    </div>\r\n\r\n    {/* روابط سطح المكتب */}\r\n<ul className=\"hidden md:flex items-center gap-3  text-sm uppercase font-medium\">\r\n\r\n  <li>\r\n    <Link href=\"/\" className=\"hover:text-orange-400 transition\">\r\n      Home\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/about\" className=\"hover:text-orange-400 transition\">\r\n      About\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/committees\" className=\"hover:text-orange-400 transition\">\r\n      Committees\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/call-for-papers\" className=\"hover:text-orange-400 transition\">\r\n      Call  Papers\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/submission\" className=\"hover:text-orange-400 transition\">\r\n      Submission\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/registration\" className=\"hover:text-orange-400 transition\">\r\n      Registration\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/venue\" className=\"hover:text-orange-400 transition\">\r\n      Venue\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/expo\" className=\"hover:text-orange-400 transition\">\r\n     Expo\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/the-best\" className=\"hover:text-orange-400 transition\">\r\n        Best\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link\r\n      href=\"/contact-us\"\r\n      scroll={true}\r\n      onClick={toggleMenu}\r\n      className=\"bg-orange-500 hover:bg-orange-600 text-white px-4 py-1.5 rounded-md text-xs font-semibold\"\r\n    >\r\n      Contact \r\n    </Link>\r\n  </li>\r\n</ul>\r\n\r\n\r\n    {/* صورة المستخدم أو زر تسجيل الدخول + زر القائمة للموبايل */}\r\n    <div className=\"flex items-center space-x-3 min-w-[100px] justify-end relative\">\r\n      {user ? (\r\n        <>\r\n          <button onClick={() => setDropdownOpen(!dropdownOpen)} className=\"focus:outline-none\">\r\n            <SafeImage\r\n              src={user.photoURL || \"/logo.png\"}\r\n              alt=\"User\"\r\n              width={36}\r\n              height={36}\r\n              className=\"rounded-full border border-white\"\r\n              fallbackSrc=\"/logo.png\"\r\n              suppressHydrationWarning\r\n            />\r\n          </button>\r\n\r\n          {dropdownOpen && (\r\n            <div\r\n              ref={dropdownRef}\r\n              className=\"absolute right-0 mt-2 w-40 bg-gray-900 border border-gray-700 rounded shadow-lg z-50\"\r\n            >\r\n              <button\r\n                onClick={handleLogout}\r\n                className=\"block w-full text-left px-4 py-2 text-sm hover:bg-orange-600 hover:text-white text-red-500\"\r\n              >\r\n                تسجيل الخروج\r\n              </button>\r\n            </div>\r\n          )}\r\n        </>\r\n      ) : (\r\n        <Link\r\n          href=\"/login\"\r\n          className=\"text-sm bg-orange-500 hover:bg-orange-600 px-3 py-1 rounded text-white\"\r\n        >\r\n          Login\r\n        </Link>\r\n      )}\r\n\r\n      {/* زر القائمة للموبايل */}\r\n      <button\r\n        className=\"md:hidden text-white\"\r\n        onClick={toggleMenu}\r\n        aria-label=\"Toggle menu\"\r\n      >\r\n        {menuOpen ? <X size={28} /> : <Menu size={28} />}\r\n      </button>\r\n    </div>\r\n  </div>\r\n\r\n  {/* القائمة الجانبية للموبايل */}\r\n  {menuOpen && (\r\n    <div className=\"md:hidden flex flex-col px-6 pb-4 pt-2 space-y-4 bg-gray-800/30 text-white backdrop-blur-md border-t border-white/10\">\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/\" onClick={toggleMenu}>Home</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/about\" onClick={toggleMenu}>About us</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/committees\" onClick={toggleMenu}>Committees</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/call-for-papers\" onClick={toggleMenu}>Call For Papers</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/submission\" onClick={toggleMenu}>Submission</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/registration\" onClick={toggleMenu}>Registration</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/venue\" onClick={toggleMenu}>Venue</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/expo\">IREGO Expo</Link>{/* Corrected href */}\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/the-best\">IREGO The Best</Link>\r\n    \r\n      \r\n      <Link\r\n        href=\"/contact-us\"\r\n        scroll={true}\r\n        onClick={toggleMenu}\r\n        className=\"block bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md text-sm font-semibold w-fit\"\r\n      >\r\n        Contact Us\r\n      </Link>\r\n    </div>\r\n  )}\r\n</nav>\r\n    \r\n    </>\r\n  );\r\n}\r\nexport default Navbar ; "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA,8NAAkD,qBAAqB;AACvE;AACA;AARA;;;;;;;;;AAUC,SAAS;IACR,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,aAAa,IAAM,YAAY,CAAC;IAEtC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,mBAAmB,KAAiB;YAC3C,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,gBAAgB;YAClB;QACF;QACA,IAAI,cAAc;YAChB,SAAS,gBAAgB,CAAC,aAAa;QACzC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;QACA,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG;QAAC;KAAa;IAEjB,MAAM,eAAe;QACnB,QAAQ,OAAO,iEAAiE;QAChF,gBAAgB;QAChB,OAAO,IAAI,CAAC,WAAW,2BAA2B;IACpD;IAEA,qBACE;kBACE,cAAA,8OAAC;YAAI,WAAU;;8BACnB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCAAC,KAAI;gCAAY,KAAI;gCAAO,OAAO;gCAAI,QAAQ;gCAAI,QAAQ;gCAAC,wBAAwB;;;;;;;;;;;sCAIhG,8OAAC;4BAAG,WAAU;;8CAEZ,8OAAC;8CACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAmC;;;;;;;;;;;8CAK9D,8OAAC;8CACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAmC;;;;;;;;;;;8CAKnE,8OAAC;8CACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAc,WAAU;kDAAmC;;;;;;;;;;;8CAKxE,8OAAC;8CACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAmB,WAAU;kDAAmC;;;;;;;;;;;8CAK7E,8OAAC;8CACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAc,WAAU;kDAAmC;;;;;;;;;;;8CAKxE,8OAAC;8CACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAgB,WAAU;kDAAmC;;;;;;;;;;;8CAK1E,8OAAC;8CACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAmC;;;;;;;;;;;8CAKnE,8OAAC;8CACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAQ,WAAU;kDAAmC;;;;;;;;;;;8CAKlE,8OAAC;8CACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAAmC;;;;;;;;;;;8CAKtE,8OAAC;8CACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,QAAQ;wCACR,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAQD,8OAAC;4BAAI,WAAU;;gCACZ,qBACC;;sDACE,8OAAC;4CAAO,SAAS,IAAM,gBAAgB,CAAC;4CAAe,WAAU;sDAC/D,cAAA,8OAAC,+HAAA,CAAA,UAAS;gDACR,KAAK,KAAK,QAAQ,IAAI;gDACtB,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;gDACV,aAAY;gDACZ,wBAAwB;;;;;;;;;;;wCAI3B,8BACC,8OAAC;4CACC,KAAK;4CACL,WAAU;sDAEV,cAAA,8OAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;iEAOP,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAMH,8OAAC;oCACC,WAAU;oCACV,SAAS;oCACT,cAAW;8CAEV,yBAAW,8OAAC,4LAAA,CAAA,IAAC;wCAAC,MAAM;;;;;6DAAS,8OAAC,kMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;gBAM/C,0BACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAI,SAAS;sCAAY;;;;;;sCACjF,8OAAC,4JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAS,SAAS;sCAAY;;;;;;sCACtF,8OAAC,4JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAc,SAAS;sCAAY;;;;;;sCAC3F,8OAAC,4JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAmB,SAAS;sCAAY;;;;;;sCAChG,8OAAC,4JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAc,SAAS;sCAAY;;;;;;sCAC3F,8OAAC,4JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAgB,SAAS;sCAAY;;;;;;sCAC7F,8OAAC,4JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAS,SAAS;sCAAY;;;;;;sCACtF,8OAAC,4JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;sCAAQ;;;;;;sCAChE,8OAAC,4JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;sCAAY;;;;;;sCAGpE,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,QAAQ;4BACR,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;AASP;uCACe", "debugId": null}}, {"offset": {"line": 564, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/components/ErrorBoundary.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\n\ninterface ErrorBoundaryState {\n  hasError: boolean;\n  error?: Error;\n}\n\ninterface ErrorBoundaryProps {\n  children: React.ReactNode;\n  fallback?: React.ComponentType<{ error: Error; resetError: () => void }>;\n}\n\nclass ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {\n  constructor(props: ErrorBoundaryProps) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError(error: Error): ErrorBoundaryState {\n    return { hasError: true, error };\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('Error caught by boundary:', error, errorInfo);\n  }\n\n  resetError = () => {\n    this.setState({ hasError: false, error: undefined });\n  };\n\n  render() {\n    if (this.state.hasError) {\n      if (this.props.fallback) {\n        const FallbackComponent = this.props.fallback;\n        return <FallbackComponent error={this.state.error!} resetError={this.resetError} />;\n      }\n\n      return (\n        <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n          <div className=\"max-w-md w-full bg-white rounded-lg shadow-md p-6 text-center\">\n            <div className=\"text-red-500 text-6xl mb-4\">⚠️</div>\n            <h2 className=\"text-2xl font-bold text-gray-800 mb-2\">Something went wrong</h2>\n            <p className=\"text-gray-600 mb-4\">\n              We're sorry, but something unexpected happened. Please try refreshing the page.\n            </p>\n            <button\n              onClick={this.resetError}\n              className=\"bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded font-semibold\"\n            >\n              Try Again\n            </button>\n            <button\n              onClick={() => window.location.reload()}\n              className=\"ml-2 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded font-semibold\"\n            >\n              Refresh Page\n            </button>\n          </div>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nexport default ErrorBoundary;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAcA,MAAM,sBAAsB,qMAAA,CAAA,UAAK,CAAC,SAAS;IACzC,YAAY,KAAyB,CAAE;QACrC,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,yBAAyB,KAAY,EAAsB;QAChE,OAAO;YAAE,UAAU;YAAM;QAAM;IACjC;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,6BAA6B,OAAO;IACpD;IAEA,aAAa;QACX,IAAI,CAAC,QAAQ,CAAC;YAAE,UAAU;YAAO,OAAO;QAAU;IACpD,EAAE;IAEF,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,MAAM,oBAAoB,IAAI,CAAC,KAAK,CAAC,QAAQ;gBAC7C,qBAAO,8OAAC;oBAAkB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;oBAAG,YAAY,IAAI,CAAC,UAAU;;;;;;YACjF;YAEA,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAA6B;;;;;;sCAC5C,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,8OAAC;4BACC,SAAS,IAAI,CAAC,UAAU;4BACxB,WAAU;sCACX;;;;;;sCAGD,8OAAC;4BACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;4BACrC,WAAU;sCACX;;;;;;;;;;;;;;;;;QAMT;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 676, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/components/ChatbotPopup.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useRef, useEffect } from 'react';\r\n\r\nconst ChatbotPopup = () => {\r\n  const [showChat, setShowChat] = useState(false);\r\n  const [messages, setMessages] = useState([\r\n    { type: 'bot', text: 'مرحباً! أنا مساعد المؤتمر الذكي. كيف يمكنني مساعدتك؟' }\r\n  ]);\r\n  const [inputText, setInputText] = useState('');\r\n  const [isTyping, setIsTyping] = useState(false);\r\n  const messagesEndRef = useRef(null);\r\n\r\n  const scrollToBottom = () => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\r\n  };\r\n\r\n  useEffect(() => {\r\n    scrollToBottom();\r\n  }, [messages]);\r\n\r\n  // قاعدة بيانات الأسئلة والأجوبة\r\n  const qaDatabase = {\r\n    'مؤتمر': 'مؤتمر الطاقة المتجددة والنفط والغاز وتغير المناخ سيقام في طرابلس، ليبيا في نوفمبر 2025',\r\n    'تاريخ': 'المؤتمر سيقام من 25-27 نوفمبر 2025',\r\n    'موعد': 'المؤتمر سيقام من 25-27 نوفمبر 2025',\r\n    'مكان': 'سيقام المؤتمر في طرابلس، ليبيا',\r\n    'طرابلس': 'نعم، المؤتمر سيقام في طرابلس، ليبيا',\r\n    'ليبيا': 'نعم، المؤتمر سيقام في طرابلس، ليبيا',\r\n    'تسجيل': 'يمكنك التسجيل من خلال الضغط على زر التسجيل في الموقع أو زيارة الرابط المخصص',\r\n    'اشتراك': 'يمكنك التسجيل من خلال الضغط على زر التسجيل في الموقع',\r\n    'أوراق': 'آخر موعد لتقديم الملخصات: 15 يونيو 2025، آخر موعد للأوراق النهائية: 1 أكتوبر 2025',\r\n    'بحث': 'آخر موعد لتقديم الملخصات: 15 يونيو 2025، آخر موعد للأوراق النهائية: 1 أكتوبر 2025',\r\n    'ملخص': 'آخر موعد لتقديم الملخصات: 15 يونيو 2025',\r\n    'رسوم': 'ستجد تفاصيل الرسوم في صفحة التسجيل',\r\n    'سعر': 'ستجد تفاصيل الرسوم في صفحة التسجيل',\r\n    'تكلفة': 'ستجد تفاصيل الرسوم في صفحة التسجيل',\r\n    'إقامة': 'سنوفر معلومات عن الفنادق والإقامة قريباً',\r\n    'فندق': 'سنوفر معلومات عن الفنادق والإقامة قريباً',\r\n    'نقل': 'سيتم توفير معلومات النقل والمواصلات قريباً',\r\n    'مواصلات': 'سيتم توفير معلومات النقل والمواصلات قريباً',\r\n    'شهادة': 'نعم، سيحصل جميع المشاركين على شهادات مشاركة',\r\n    'شهادات': 'نعم، سيحصل جميع المشاركين على شهادات مشاركة',\r\n    'لغة': 'المؤتمر سيكون باللغتين العربية والإنجليزية',\r\n    'عربي': 'المؤتمر سيكون باللغتين العربية والإنجليزية',\r\n    'انجليزي': 'المؤتمر سيكون باللغتين العربية والإنجليزية',\r\n    'مواضيع': 'المواضيع تشمل: الطاقة المتجددة، النفط والغاز، تغير المناخ، التنمية المستدامة',\r\n    'طاقة': 'المؤتمر يركز على الطاقة المتجددة والطاقة الشمسية وطاقة الرياح',\r\n    'نفط': 'المؤتمر يغطي مواضيع النفط والغاز والانتقال للطاقة النظيفة',\r\n    'غاز': 'المؤتمر يغطي مواضيع النفط والغاز والانتقال للطاقة النظيفة',\r\n    'مناخ': 'المؤتمر يناقش تغير المناخ والحلول البيئية',\r\n    'بيئة': 'المؤتمر يناقش القضايا البيئية والتنمية المستدامة',\r\n    'متحدثين': 'سيشارك خبراء وباحثون وصناع قرار من جميع أنحاء العالم',\r\n    'خبراء': 'سيشارك خبراء وباحثون وصناع قرار من جميع أنحاء العالم',\r\n    'معارض': 'نعم، سيكون هناك معرض مصاحب للمؤتمر',\r\n    'معرض': 'نعم، سيكون هناك معرض مصاحب للمؤتمر',\r\n    'ورش': 'سيتم تنظيم ورش عمل متخصصة خلال المؤتمر',\r\n    'ورشة': 'سيتم تنظيم ورش عمل متخصصة خلال المؤتمر',\r\n    'مرحبا': 'مرحباً بك! كيف يمكنني مساعدتك بخصوص المؤتمر؟',\r\n    'شكرا': 'عفواً! سعيد لمساعدتك. هل لديك أسئلة أخرى؟',\r\n    'مساعدة': 'يمكنني مساعدتك في أي استفسار حول المؤتمر. اسأل عن أي شيء!',\r\n    'معلومات': 'يمكنني تقديم معلومات عن التاريخ، المكان، التسجيل، الأوراق، والمزيد'\r\n  };\r\n\r\n  const findAnswer = (question) => {\r\n    const lowerQuestion = question.toLowerCase();\r\n    for (const [keyword, answer] of Object.entries(qaDatabase)) {\r\n      if (lowerQuestion.includes(keyword)) {\r\n        return answer;\r\n      }\r\n    }\r\n    return 'عذراً، لم أفهم سؤالك. يمكنك السؤال عن: المؤتمر، التاريخ، المكان، التسجيل، الأوراق، الرسوم، الإقامة، النقل، الشهادات، اللغة، المواضيع، المتحدثين، المعارض، ورش العمل';\r\n  };\r\n\r\n  const handleSendMessage = () => {\r\n    if (!inputText.trim()) return;\r\n\r\n    const userMessage = { type: 'user', text: inputText };\r\n    setMessages(prev => [...prev, userMessage]);\r\n    setInputText('');\r\n    setIsTyping(true);\r\n\r\n    // محاكاة تأخير الرد\r\n    setTimeout(() => {\r\n      const botResponse = findAnswer(inputText);\r\n      const botMessage = { type: 'bot', text: botResponse };\r\n      setMessages(prev => [...prev, botMessage]);\r\n      setIsTyping(false);\r\n    }, 1000);\r\n  };\r\n\r\n  const handleKeyDown = (e) => {\r\n    if (e.key === 'Enter') {\r\n      handleSendMessage();\r\n    }\r\n  };\r\n\r\n  const toggleChat = () => {\r\n    setShowChat(!showChat);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {/* زر ثابت في الركن السفلي الأيمن */}\r\n      <button\r\n        style={{\r\n          position: 'fixed',\r\n          bottom: '20px',\r\n          right: '20px',\r\n          zIndex: 1000,\r\n          padding: '15px 20px',\r\n          background: 'linear-gradient(45deg, #007bff, #0056b3)',\r\n          color: 'white',\r\n          border: 'none',\r\n          borderRadius: '50px',\r\n          cursor: 'pointer',\r\n          boxShadow: '0 4px 15px rgba(0,123,255,0.3)',\r\n          fontSize: '16px',\r\n          fontWeight: 'bold',\r\n          transition: 'all 0.3s ease',\r\n          transform: showChat ? 'scale(0.9)' : 'scale(1)',\r\n        }}\r\n        onClick={toggleChat}\r\n        onMouseEnter={(e) => {\r\n          e.target.style.transform = 'scale(1.1)';\r\n          e.target.style.boxShadow = '0 6px 20px rgba(0,123,255,0.4)';\r\n        }}\r\n        onMouseLeave={(e) => {\r\n          e.target.style.transform = showChat ? 'scale(0.9)' : 'scale(1)';\r\n          e.target.style.boxShadow = '0 4px 15px rgba(0,123,255,0.3)';\r\n        }}\r\n      >\r\n        {showChat ? '✕' : '💬 Chat'}\r\n      </button>\r\n\r\n      {/* الصفحة اللي بتظهر عند الضغط */}\r\n      {showChat && (\r\n        <div\r\n          style={{\r\n            position: 'fixed',\r\n            bottom: '70px',\r\n            right: '20px',\r\n            width: '300px',\r\n            height: '400px',\r\n            backgroundColor: 'white',\r\n            boxShadow: '0 4px 8px rgba(0,0,0,0.2)',\r\n            borderRadius: '10px',\r\n            zIndex: 999,\r\n            display: 'flex',\r\n            flexDirection: 'column',\r\n          }}\r\n        >\r\n          {/* رأس الـ Chatbot */}\r\n          <div\r\n            style={{\r\n              backgroundColor: '#007bff',\r\n              color: 'white',\r\n              padding: '10px',\r\n              borderTopLeftRadius: '10px',\r\n              borderTopRightRadius: '10px',\r\n            }}\r\n          >\r\n            <h4 style={{ margin: 0 }}>محادثة الدعم</h4>\r\n          </div>\r\n          \r\n          {/* محتوى الـ Chatbot */}\r\n          <div style={{ flex: 1, padding: '10px', overflowY: 'auto', display: 'flex', flexDirection: 'column' }}>\r\n            {/* الرسائل */}\r\n            <div style={{ flex: 1, overflowY: 'auto', marginBottom: '10px' }}>\r\n              {messages.map((message, index) => (\r\n                <div\r\n                  key={index}\r\n                  style={{\r\n                    marginBottom: '10px',\r\n                    display: 'flex',\r\n                    justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start'\r\n                  }}\r\n                >\r\n                  <div\r\n                    style={{\r\n                      maxWidth: '80%',\r\n                      padding: '8px 12px',\r\n                      borderRadius: '15px',\r\n                      backgroundColor: message.type === 'user' ? '#007bff' : '#f1f1f1',\r\n                      color: message.type === 'user' ? 'white' : 'black',\r\n                      fontSize: '14px',\r\n                      lineHeight: '1.4'\r\n                    }}\r\n                  >\r\n                    {message.text}\r\n                  </div>\r\n                </div>\r\n              ))}\r\n              {isTyping && (\r\n                <div style={{ display: 'flex', justifyContent: 'flex-start', marginBottom: '10px' }}>\r\n                  <div\r\n                    style={{\r\n                      padding: '8px 12px',\r\n                      borderRadius: '15px',\r\n                      backgroundColor: '#f1f1f1',\r\n                      fontSize: '14px'\r\n                    }}\r\n                  >\r\n                    يكتب...\r\n                  </div>\r\n                </div>\r\n              )}\r\n              <div ref={messagesEndRef} />\r\n            </div>\r\n\r\n            {/* مربع الإدخال */}\r\n            <div style={{ display: 'flex', gap: '5px' }}>\r\n              <input\r\n                type=\"text\"\r\n                value={inputText}\r\n                onChange={(e) => setInputText(e.target.value)}\r\n                onKeyDown={handleKeyDown}\r\n                placeholder=\"اكتب رسالتك هنا...\"\r\n                style={{\r\n                  flex: 1,\r\n                  padding: '8px',\r\n                  border: '1px solid #ddd',\r\n                  borderRadius: '20px',\r\n                  outline: 'none',\r\n                  fontSize: '14px'\r\n                }}\r\n              />\r\n              <button\r\n                onClick={handleSendMessage}\r\n                style={{\r\n                  padding: '8px 15px',\r\n                  backgroundColor: '#007bff',\r\n                  color: 'white',\r\n                  border: 'none',\r\n                  borderRadius: '20px',\r\n                  cursor: 'pointer',\r\n                  fontSize: '14px'\r\n                }}\r\n              >\r\n                إرسال\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* زر إغلاق */}\r\n          <button\r\n            style={{\r\n              position: 'absolute',\r\n              top: '5px',\r\n              right: '10px',\r\n              background: 'transparent',\r\n              border: 'none',\r\n              fontSize: '20px',\r\n              cursor: 'pointer',\r\n            }}\r\n            onClick={toggleChat}\r\n          >\r\n            &times;\r\n          </button>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ChatbotPopup;"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,eAAe;IACnB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC;YAAE,MAAM;YAAO,MAAM;QAAuD;KAC7E;IACD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE9B,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAS;IAEb,gCAAgC;IAChC,MAAM,aAAa;QACjB,SAAS;QACT,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,SAAS;QACT,SAAS;QACT,UAAU;QACV,SAAS;QACT,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,OAAO;QACP,WAAW;QACX,SAAS;QACT,UAAU;QACV,OAAO;QACP,QAAQ;QACR,WAAW;QACX,UAAU;QACV,QAAQ;QACR,OAAO;QACP,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,WAAW;QACX,SAAS;QACT,SAAS;QACT,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;IACb;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,gBAAgB,SAAS,WAAW;QAC1C,KAAK,MAAM,CAAC,SAAS,OAAO,IAAI,OAAO,OAAO,CAAC,YAAa;YAC1D,IAAI,cAAc,QAAQ,CAAC,UAAU;gBACnC,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,UAAU,IAAI,IAAI;QAEvB,MAAM,cAAc;YAAE,MAAM;YAAQ,MAAM;QAAU;QACpD,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,aAAa;QACb,YAAY;QAEZ,oBAAoB;QACpB,WAAW;YACT,MAAM,cAAc,WAAW;YAC/B,MAAM,aAAa;gBAAE,MAAM;gBAAO,MAAM;YAAY;YACpD,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAW;YACzC,YAAY;QACd,GAAG;IACL;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB;QACF;IACF;IAEA,MAAM,aAAa;QACjB,YAAY,CAAC;IACf;IAEA,qBACE;;0BAEE,8OAAC;gBACC,OAAO;oBACL,UAAU;oBACV,QAAQ;oBACR,OAAO;oBACP,QAAQ;oBACR,SAAS;oBACT,YAAY;oBACZ,OAAO;oBACP,QAAQ;oBACR,cAAc;oBACd,QAAQ;oBACR,WAAW;oBACX,UAAU;oBACV,YAAY;oBACZ,YAAY;oBACZ,WAAW,WAAW,eAAe;gBACvC;gBACA,SAAS;gBACT,cAAc,CAAC;oBACb,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;oBAC3B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;gBAC7B;gBACA,cAAc,CAAC;oBACb,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG,WAAW,eAAe;oBACrD,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;gBAC7B;0BAEC,WAAW,MAAM;;;;;;YAInB,0BACC,8OAAC;gBACC,OAAO;oBACL,UAAU;oBACV,QAAQ;oBACR,OAAO;oBACP,OAAO;oBACP,QAAQ;oBACR,iBAAiB;oBACjB,WAAW;oBACX,cAAc;oBACd,QAAQ;oBACR,SAAS;oBACT,eAAe;gBACjB;;kCAGA,8OAAC;wBACC,OAAO;4BACL,iBAAiB;4BACjB,OAAO;4BACP,SAAS;4BACT,qBAAqB;4BACrB,sBAAsB;wBACxB;kCAEA,cAAA,8OAAC;4BAAG,OAAO;gCAAE,QAAQ;4BAAE;sCAAG;;;;;;;;;;;kCAI5B,8OAAC;wBAAI,OAAO;4BAAE,MAAM;4BAAG,SAAS;4BAAQ,WAAW;4BAAQ,SAAS;4BAAQ,eAAe;wBAAS;;0CAElG,8OAAC;gCAAI,OAAO;oCAAE,MAAM;oCAAG,WAAW;oCAAQ,cAAc;gCAAO;;oCAC5D,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;4CAEC,OAAO;gDACL,cAAc;gDACd,SAAS;gDACT,gBAAgB,QAAQ,IAAI,KAAK,SAAS,aAAa;4CACzD;sDAEA,cAAA,8OAAC;gDACC,OAAO;oDACL,UAAU;oDACV,SAAS;oDACT,cAAc;oDACd,iBAAiB,QAAQ,IAAI,KAAK,SAAS,YAAY;oDACvD,OAAO,QAAQ,IAAI,KAAK,SAAS,UAAU;oDAC3C,UAAU;oDACV,YAAY;gDACd;0DAEC,QAAQ,IAAI;;;;;;2CAlBV;;;;;oCAsBR,0BACC,8OAAC;wCAAI,OAAO;4CAAE,SAAS;4CAAQ,gBAAgB;4CAAc,cAAc;wCAAO;kDAChF,cAAA,8OAAC;4CACC,OAAO;gDACL,SAAS;gDACT,cAAc;gDACd,iBAAiB;gDACjB,UAAU;4CACZ;sDACD;;;;;;;;;;;kDAKL,8OAAC;wCAAI,KAAK;;;;;;;;;;;;0CAIZ,8OAAC;gCAAI,OAAO;oCAAE,SAAS;oCAAQ,KAAK;gCAAM;;kDACxC,8OAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;wCAC5C,WAAW;wCACX,aAAY;wCACZ,OAAO;4CACL,MAAM;4CACN,SAAS;4CACT,QAAQ;4CACR,cAAc;4CACd,SAAS;4CACT,UAAU;wCACZ;;;;;;kDAEF,8OAAC;wCACC,SAAS;wCACT,OAAO;4CACL,SAAS;4CACT,iBAAiB;4CACjB,OAAO;4CACP,QAAQ;4CACR,cAAc;4CACd,QAAQ;4CACR,UAAU;wCACZ;kDACD;;;;;;;;;;;;;;;;;;kCAOL,8OAAC;wBACC,OAAO;4BACL,UAAU;4BACV,KAAK;4BACL,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,UAAU;4BACV,QAAQ;wBACV;wBACA,SAAS;kCACV;;;;;;;;;;;;;;AAOX;uCAEe", "debugId": null}}]}