.swiper-fade.swiper-free-mode ::slotted(swiper-slide) {
  transition-timing-function: ease-out;
}
.swiper-fade ::slotted(swiper-slide) {
  pointer-events: none;
  transition-property: opacity;
}
.swiper-fade ::slotted(swiper-slide) ::slotted(swiper-slide) {
  pointer-events: none;
}
.swiper-fade ::slotted(.swiper-slide-active) {
  pointer-events: auto;
}
.swiper-fade ::slotted(.swiper-slide-active) ::slotted(.swiper-slide-active) {
  pointer-events: auto;
}
