{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/lib/api.ts"], "sourcesContent": ["// API configuration and helper functions\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';\n\n// Fallback mode for testing when <PERSON><PERSON> is not available\nconst FALLBACK_MODE = false; // Set to true if <PERSON><PERSON> is not working\n\n// Types\nexport interface User {\n  id: number;\n  name: string;\n  email: string;\n  email_verified_at?: string;\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface AuthResponse {\n  success: boolean;\n  message: string;\n  data?: {\n    user: User;\n    token: string;\n    token_type: string;\n    expires_in: number;\n  };\n}\n\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  message: string;\n  data?: T;\n}\n\n// Helper function to get auth token from localStorage\nconst getAuthToken = (): string | null => {\n  if (typeof window !== 'undefined') {\n    return localStorage.getItem('auth_token');\n  }\n  return null;\n};\n\n// Helper function to set auth token in localStorage\nconst setAuthToken = (token: string): void => {\n  if (typeof window !== 'undefined') {\n    localStorage.setItem('auth_token', token);\n  }\n};\n\n// Helper function to remove auth token from localStorage\nconst removeAuthToken = (): void => {\n  if (typeof window !== 'undefined') {\n    localStorage.removeItem('auth_token');\n  }\n};\n\n// Generic API call function\nconst apiCall = async <T = any>(\n  endpoint: string,\n  options: RequestInit = {}\n): Promise<ApiResponse<T>> => {\n  const token = getAuthToken();\n  \n  const defaultHeaders: HeadersInit = {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json',\n  };\n\n  if (token) {\n    defaultHeaders['Authorization'] = `Bearer ${token}`;\n  }\n\n  const config: RequestInit = {\n    ...options,\n    headers: {\n      ...defaultHeaders,\n      ...options.headers,\n    },\n  };\n\n  try {\n    const response = await fetch(`${API_BASE_URL}${endpoint}`, config);\n\n    if (!response.ok) {\n      // Try to get error message from response\n      try {\n        const errorData = await response.json();\n        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);\n      } catch {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n      }\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('API Error:', error);\n\n    // Handle network errors\n    if (error instanceof TypeError && error.message.includes('fetch')) {\n      throw new Error('Network error: Unable to connect to server. Please check if the backend is running.');\n    }\n\n    throw error;\n  }\n};\n\n// Authentication API calls\nexport const authAPI = {\n  // Login\n  login: async (email: string, password: string): Promise<AuthResponse> => {\n    // Fallback for testing when Laravel is not available\n    if (FALLBACK_MODE) {\n      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate network delay\n      if (email && password) {\n        const mockResponse: AuthResponse = {\n          success: true,\n          message: 'Login successful',\n          data: {\n            user: {\n              id: 1,\n              name: 'Test User',\n              email: email,\n              created_at: new Date().toISOString(),\n              updated_at: new Date().toISOString()\n            },\n            token: 'mock-jwt-token-' + Date.now(),\n            token_type: 'bearer',\n            expires_in: 3600\n          }\n        };\n        setAuthToken(mockResponse.data!.token);\n        return mockResponse;\n      } else {\n        return {\n          success: false,\n          message: 'Invalid credentials'\n        };\n      }\n    }\n\n    const response = await apiCall<AuthResponse['data']>('/auth/login', {\n      method: 'POST',\n      body: JSON.stringify({ email, password }),\n    });\n\n    if (response.success && response.data?.token) {\n      setAuthToken(response.data.token);\n    }\n\n    return response as AuthResponse;\n  },\n\n  // Register\n  register: async (name: string, email: string, password: string, password_confirmation: string): Promise<AuthResponse> => {\n    // Fallback for testing when Laravel is not available\n    if (FALLBACK_MODE) {\n      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate network delay\n      if (name && email && password) {\n        const mockResponse: AuthResponse = {\n          success: true,\n          message: 'Registration successful',\n          data: {\n            user: {\n              id: 2,\n              name: name,\n              email: email,\n              created_at: new Date().toISOString(),\n              updated_at: new Date().toISOString()\n            },\n            token: 'mock-jwt-token-' + Date.now(),\n            token_type: 'bearer',\n            expires_in: 3600\n          }\n        };\n        setAuthToken(mockResponse.data!.token);\n        return mockResponse;\n      } else {\n        return {\n          success: false,\n          message: 'All fields are required'\n        };\n      }\n    }\n\n    const response = await apiCall<AuthResponse['data']>('/auth/register', {\n      method: 'POST',\n      body: JSON.stringify({\n        name,\n        email,\n        password,\n        password_confirmation\n      }),\n    });\n\n    if (response.success && response.data?.token) {\n      setAuthToken(response.data.token);\n    }\n\n    return response as AuthResponse;\n  },\n\n  // Logout\n  logout: async (): Promise<ApiResponse> => {\n    try {\n      const response = await apiCall('/auth/logout', {\n        method: 'POST',\n      });\n      removeAuthToken();\n      return response;\n    } catch (error) {\n      // Even if API call fails, remove token locally\n      removeAuthToken();\n      throw error;\n    }\n  },\n\n  // Get current user\n  me: async (): Promise<ApiResponse<User>> => {\n    return apiCall<User>('/auth/me');\n  },\n\n  // Refresh token\n  refresh: async (): Promise<AuthResponse> => {\n    const response = await apiCall<AuthResponse['data']>('/auth/refresh', {\n      method: 'POST',\n    });\n\n    if (response.success && response.data?.token) {\n      setAuthToken(response.data.token);\n    }\n\n    return response as AuthResponse;\n  },\n};\n\n// Conference API calls\nexport const conferenceAPI = {\n  getAll: () => apiCall('/conferences'),\n  getCurrent: () => apiCall('/conferences/current'),\n  getById: (id: number) => apiCall(`/conferences/${id}`),\n};\n\n// Speaker API calls\nexport const speakerAPI = {\n  getAll: () => apiCall('/speakers'),\n  getById: (id: number) => apiCall(`/speakers/${id}`),\n  getCommittees: () => apiCall('/speakers/committees/all'),\n  getKeynotes: () => apiCall('/speakers/keynotes/all'),\n};\n\n// News API calls\nexport const newsAPI = {\n  getAll: () => apiCall('/news'),\n  getFeatured: () => apiCall('/news/featured'),\n  getRecent: () => apiCall('/news/recent'),\n  getBySlug: (slug: string) => apiCall(`/news/${slug}`),\n};\n\n// Sponsor API calls\nexport const sponsorAPI = {\n  getAll: () => apiCall('/sponsors'),\n  getById: (id: number) => apiCall(`/sponsors/${id}`),\n};\n\n// Event API calls\nexport const eventAPI = {\n  getAll: () => apiCall('/events'),\n  getById: (id: number) => apiCall(`/events/${id}`),\n  getSchedule: () => apiCall('/events/schedule/all'),\n};\n\n// Registration API calls\nexport const registrationAPI = {\n  create: (data: any) => apiCall('/registrations', {\n    method: 'POST',\n    body: JSON.stringify(data),\n  }),\n  getByConfirmationCode: (code: string) => apiCall(`/registrations/${code}`),\n  checkStatus: (data: any) => apiCall('/registrations/check-status', {\n    method: 'POST',\n    body: JSON.stringify(data),\n  }),\n};\n\n// Export utility functions\nexport { getAuthToken, setAuthToken, removeAuthToken };\n"], "names": [], "mappings": "AAAA,yCAAyC;;;;;;;;;;;;;AACpB;AAArB,MAAM,eAAe,oEAAmC;AAExD,0DAA0D;AAC1D,MAAM,gBAAgB,OAAO,wCAAwC;AA6BrE,sDAAsD;AACtD,MAAM,eAAe;IACnB,wCAAmC;QACjC,OAAO,aAAa,OAAO,CAAC;IAC9B;;AAEF;AAEA,oDAAoD;AACpD,MAAM,eAAe,CAAC;IACpB,wCAAmC;QACjC,aAAa,OAAO,CAAC,cAAc;IACrC;AACF;AAEA,yDAAyD;AACzD,MAAM,kBAAkB;IACtB,wCAAmC;QACjC,aAAa,UAAU,CAAC;IAC1B;AACF;AAEA,4BAA4B;AAC5B,MAAM,UAAU,OACd,UACA,UAAuB,CAAC,CAAC;IAEzB,MAAM,QAAQ;IAEd,MAAM,iBAA8B;QAClC,gBAAgB;QAChB,UAAU;IACZ;IAEA,IAAI,OAAO;QACT,cAAc,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;IACrD;IAEA,MAAM,SAAsB;QAC1B,GAAG,OAAO;QACV,SAAS;YACP,GAAG,cAAc;YACjB,GAAG,QAAQ,OAAO;QACpB;IACF;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,UAAU,EAAE;QAE3D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,yCAAyC;YACzC,IAAI;gBACF,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;YACxF,EAAE,OAAM;gBACN,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;YACnE;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAE5B,wBAAwB;QACxB,IAAI,iBAAiB,aAAa,MAAM,OAAO,CAAC,QAAQ,CAAC,UAAU;YACjE,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM;IACR;AACF;AAGO,MAAM,UAAU;IACrB,QAAQ;IACR,OAAO,OAAO,OAAe;QAC3B,qDAAqD;QACrD,uCAAmB;;QA2BnB;QAEA,MAAM,WAAW,MAAM,QAA8B,eAAe;YAClE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAO;YAAS;QACzC;QAEA,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE,OAAO;YAC5C,aAAa,SAAS,IAAI,CAAC,KAAK;QAClC;QAEA,OAAO;IACT;IAEA,WAAW;IACX,UAAU,OAAO,MAAc,OAAe,UAAkB;QAC9D,qDAAqD;QACrD,uCAAmB;;QA2BnB;QAEA,MAAM,WAAW,MAAM,QAA8B,kBAAkB;YACrE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBACnB;gBACA;gBACA;gBACA;YACF;QACF;QAEA,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE,OAAO;YAC5C,aAAa,SAAS,IAAI,CAAC,KAAK;QAClC;QAEA,OAAO;IACT;IAEA,SAAS;IACT,QAAQ;QACN,IAAI;YACF,MAAM,WAAW,MAAM,QAAQ,gBAAgB;gBAC7C,QAAQ;YACV;YACA;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,+CAA+C;YAC/C;YACA,MAAM;QACR;IACF;IAEA,mBAAmB;IACnB,IAAI;QACF,OAAO,QAAc;IACvB;IAEA,gBAAgB;IAChB,SAAS;QACP,MAAM,WAAW,MAAM,QAA8B,iBAAiB;YACpE,QAAQ;QACV;QAEA,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE,OAAO;YAC5C,aAAa,SAAS,IAAI,CAAC,KAAK;QAClC;QAEA,OAAO;IACT;AACF;AAGO,MAAM,gBAAgB;IAC3B,QAAQ,IAAM,QAAQ;IACtB,YAAY,IAAM,QAAQ;IAC1B,SAAS,CAAC,KAAe,QAAQ,CAAC,aAAa,EAAE,IAAI;AACvD;AAGO,MAAM,aAAa;IACxB,QAAQ,IAAM,QAAQ;IACtB,SAAS,CAAC,KAAe,QAAQ,CAAC,UAAU,EAAE,IAAI;IAClD,eAAe,IAAM,QAAQ;IAC7B,aAAa,IAAM,QAAQ;AAC7B;AAGO,MAAM,UAAU;IACrB,QAAQ,IAAM,QAAQ;IACtB,aAAa,IAAM,QAAQ;IAC3B,WAAW,IAAM,QAAQ;IACzB,WAAW,CAAC,OAAiB,QAAQ,CAAC,MAAM,EAAE,MAAM;AACtD;AAGO,MAAM,aAAa;IACxB,QAAQ,IAAM,QAAQ;IACtB,SAAS,CAAC,KAAe,QAAQ,CAAC,UAAU,EAAE,IAAI;AACpD;AAGO,MAAM,WAAW;IACtB,QAAQ,IAAM,QAAQ;IACtB,SAAS,CAAC,KAAe,QAAQ,CAAC,QAAQ,EAAE,IAAI;IAChD,aAAa,IAAM,QAAQ;AAC7B;AAGO,MAAM,kBAAkB;IAC7B,QAAQ,CAAC,OAAc,QAAQ,kBAAkB;YAC/C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACA,uBAAuB,CAAC,OAAiB,QAAQ,CAAC,eAAe,EAAE,MAAM;IACzE,aAAa,CAAC,OAAc,QAAQ,+BAA+B;YACjE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;AACF", "debugId": null}}, {"offset": {"line": 197, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/context/UserContext.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from \"react\";\r\nimport { authAPI, getAuthToken, removeAuthToken, User as ApiUser } from \"../lib/api\";\r\n\r\ntype User = {\r\n  id?: number;\r\n  name?: string;\r\n  email?: string;\r\n  photoURL?: string;\r\n  email_verified_at?: string;\r\n  created_at?: string;\r\n  updated_at?: string;\r\n};\r\n\r\ntype UserContextType = {\r\n  user: User | null;\r\n  setUser: (user: User | null) => void;\r\n  login: (email: string, password: string) => Promise<{ success: boolean; message: string }>;\r\n  register: (name: string, email: string, password: string, passwordConfirmation: string) => Promise<{ success: boolean; message: string }>;\r\n  logout: () => Promise<void>;\r\n  loading: boolean;\r\n  isAuthenticated: boolean;\r\n};\r\n\r\nconst UserContext = createContext<UserContextType | undefined>(undefined);\r\n\r\nexport function UserProvider({ children }: { children: ReactNode }) {\r\n  const [user, setUser] = useState<User | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  // Check if user is authenticated\r\n  const isAuthenticated = !!user && !!getAuthToken();\r\n\r\n  // Load user from token on app start\r\n  useEffect(() => {\r\n    const loadUser = async () => {\r\n      const token = getAuthToken();\r\n      if (token) {\r\n        try {\r\n          const response = await authAPI.me();\r\n          if (response.success && response.data) {\r\n            setUser(response.data);\r\n          } else {\r\n            // Token is invalid, remove it\r\n            removeAuthToken();\r\n          }\r\n        } catch (error) {\r\n          console.error('Failed to load user:', error);\r\n          removeAuthToken();\r\n        }\r\n      }\r\n      setLoading(false);\r\n    };\r\n\r\n    loadUser();\r\n  }, []);\r\n\r\n  // Login function\r\n  const login = async (email: string, password: string) => {\r\n    try {\r\n      const response = await authAPI.login(email, password);\r\n      if (response.success && response.data) {\r\n        setUser(response.data.user);\r\n        return { success: true, message: 'Login successful' };\r\n      } else {\r\n        return { success: false, message: response.message || 'Login failed' };\r\n      }\r\n    } catch (error: any) {\r\n      return { success: false, message: error.message || 'Login failed' };\r\n    }\r\n  };\r\n\r\n  // Register function\r\n  const register = async (name: string, email: string, password: string, passwordConfirmation: string) => {\r\n    try {\r\n      const response = await authAPI.register(name, email, password, passwordConfirmation);\r\n      if (response.success && response.data) {\r\n        setUser(response.data.user);\r\n        return { success: true, message: 'Registration successful' };\r\n      } else {\r\n        return { success: false, message: response.message || 'Registration failed' };\r\n      }\r\n    } catch (error: any) {\r\n      return { success: false, message: error.message || 'Registration failed' };\r\n    }\r\n  };\r\n\r\n  // Logout function\r\n  const logout = async () => {\r\n    try {\r\n      await authAPI.logout();\r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n    } finally {\r\n      setUser(null);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <UserContext.Provider value={{\r\n      user,\r\n      setUser,\r\n      login,\r\n      register,\r\n      logout,\r\n      loading,\r\n      isAuthenticated\r\n    }}>\r\n      {children}\r\n    </UserContext.Provider>\r\n  );\r\n}\r\n\r\nexport function useUser() {\r\n  const context = useContext(UserContext);\r\n  if (!context) {\r\n    throw new Error(\"useUser must be used within a UserProvider\");\r\n  }\r\n  return context;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAyBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAA2B;;IAChE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,iCAAiC;IACjC,MAAM,kBAAkB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD;IAE/C,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;mDAAW;oBACf,MAAM,QAAQ,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD;oBACzB,IAAI,OAAO;wBACT,IAAI;4BACF,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,EAAE;4BACjC,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gCACrC,QAAQ,SAAS,IAAI;4BACvB,OAAO;gCACL,8BAA8B;gCAC9B,CAAA,GAAA,oHAAA,CAAA,kBAAe,AAAD;4BAChB;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,wBAAwB;4BACtC,CAAA,GAAA,oHAAA,CAAA,kBAAe,AAAD;wBAChB;oBACF;oBACA,WAAW;gBACb;;YAEA;QACF;iCAAG,EAAE;IAEL,iBAAiB;IACjB,MAAM,QAAQ,OAAO,OAAe;QAClC,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,KAAK,CAAC,OAAO;YAC5C,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,QAAQ,SAAS,IAAI,CAAC,IAAI;gBAC1B,OAAO;oBAAE,SAAS;oBAAM,SAAS;gBAAmB;YACtD,OAAO;gBACL,OAAO;oBAAE,SAAS;oBAAO,SAAS,SAAS,OAAO,IAAI;gBAAe;YACvE;QACF,EAAE,OAAO,OAAY;YACnB,OAAO;gBAAE,SAAS;gBAAO,SAAS,MAAM,OAAO,IAAI;YAAe;QACpE;IACF;IAEA,oBAAoB;IACpB,MAAM,WAAW,OAAO,MAAc,OAAe,UAAkB;QACrE,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,MAAM,OAAO,UAAU;YAC/D,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,QAAQ,SAAS,IAAI,CAAC,IAAI;gBAC1B,OAAO;oBAAE,SAAS;oBAAM,SAAS;gBAA0B;YAC7D,OAAO;gBACL,OAAO;oBAAE,SAAS;oBAAO,SAAS,SAAS,OAAO,IAAI;gBAAsB;YAC9E;QACF,EAAE,OAAO,OAAY;YACnB,OAAO;gBAAE,SAAS;gBAAO,SAAS,MAAM,OAAO,IAAI;YAAsB;QAC3E;IACF;IAEA,kBAAkB;IAClB,MAAM,SAAS;QACb,IAAI;YACF,MAAM,oHAAA,CAAA,UAAO,CAAC,MAAM;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,QAAQ;QACV;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;YAC3B;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBACG;;;;;;AAGP;GArFgB;KAAA;AAuFT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 337, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/components/Navbar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport Image from \"next/image\";\r\nimport { Menu, X } from \"lucide-react\";\r\nimport { useUser } from \"../context/UserContext\"; // عدل حسب مكان الملف\r\nimport { useRouter } from \"next/navigation\";\r\n\r\n function Navbar() {\r\n  const [menuOpen, setMenuOpen] = useState(false);\r\n  const [dropdownOpen, setDropdownOpen] = useState(false);\r\n  const toggleMenu = () => setMenuOpen(!menuOpen);\r\n\r\n  const { user, logout } = useUser();\r\n  const router = useRouter();\r\n\r\n  const dropdownRef = useRef<HTMLDivElement>(null);\r\n\r\n  // إغلاق الـ dropdown عند الضغط خارجها\r\n  useEffect(() => {\r\n    function handleClickOutside(event: MouseEvent) {\r\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\r\n        setDropdownOpen(false);\r\n      }\r\n    }\r\n    if (dropdownOpen) {\r\n      document.addEventListener(\"mousedown\", handleClickOutside);\r\n    } else {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    }\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, [dropdownOpen]);\r\n\r\n  const handleLogout = async () => {\r\n    try {\r\n      await logout();\r\n      setDropdownOpen(false);\r\n      router.push(\"/login\");\r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n      // Even if logout fails, redirect to login\r\n      setDropdownOpen(false);\r\n      router.push(\"/login\");\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <nav className=\"absolute top-[35px] left-0 w-full bg-gray-800/30 text-white z-40 backdrop-blur-md border-b border-white/10\">\r\n  <div className=\"max-w-7xl mx-auto px-4 py-3 flex items-center justify-between h-16\">\r\n    {/* الشعار */}\r\n    <div className=\"flex items-center space-x-2 min-w-[60px]\">\r\n      <Image src=\"/logo.png\" alt=\"Logo\" width={50} height={40} />\r\n    </div>\r\n\r\n    {/* روابط سطح المكتب */}\r\n<ul className=\"hidden md:flex items-center gap-3  text-sm uppercase font-medium\">\r\n\r\n  <li>\r\n    <Link href=\"/\" className=\"hover:text-orange-400 transition\">\r\n      Home\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/about\" className=\"hover:text-orange-400 transition\">\r\n      About\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/committees\" className=\"hover:text-orange-400 transition\">\r\n      Committees\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/call-for-papers\" className=\"hover:text-orange-400 transition\">\r\n      Call  Papers\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/submission\" className=\"hover:text-orange-400 transition\">\r\n      Submission\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/registration\" className=\"hover:text-orange-400 transition\">\r\n      Registration\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/venue\" className=\"hover:text-orange-400 transition\">\r\n      Venue\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/expo\" className=\"hover:text-orange-400 transition\">\r\n     Expo\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/the-best\" className=\"hover:text-orange-400 transition\">\r\n        Best\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link\r\n      href=\"/contact-us\"\r\n      scroll={true}\r\n      onClick={toggleMenu}\r\n      className=\"bg-orange-500 hover:bg-orange-600 text-white px-4 py-1.5 rounded-md text-xs font-semibold\"\r\n    >\r\n      Contact \r\n    </Link>\r\n  </li>\r\n</ul>\r\n\r\n\r\n    {/* صورة المستخدم أو زر تسجيل الدخول + زر القائمة للموبايل */}\r\n    <div className=\"flex items-center space-x-3 min-w-[100px] justify-end relative\">\r\n      {user ? (\r\n        <>\r\n          <button onClick={() => setDropdownOpen(!dropdownOpen)} className=\"focus:outline-none\">\r\n            <Image\r\n              src={user.photoURL || \"/avatar.png\"}\r\n              alt=\"User\"\r\n              width={36}\r\n              height={36}\r\n              className=\"rounded-full border border-white\"\r\n            />\r\n          </button>\r\n\r\n          {dropdownOpen && (\r\n            <div\r\n              ref={dropdownRef}\r\n              className=\"absolute right-0 mt-2 w-40 bg-gray-900 border border-gray-700 rounded shadow-lg z-50\"\r\n            >\r\n              <button\r\n                onClick={handleLogout}\r\n                className=\"block w-full text-left px-4 py-2 text-sm hover:bg-orange-600 hover:text-white text-red-500\"\r\n              >\r\n                تسجيل الخروج\r\n              </button>\r\n            </div>\r\n          )}\r\n        </>\r\n      ) : (\r\n        <Link\r\n          href=\"/login\"\r\n          className=\"text-sm bg-orange-500 hover:bg-orange-600 px-3 py-1 rounded text-white\"\r\n        >\r\n          Login\r\n        </Link>\r\n      )}\r\n\r\n      {/* زر القائمة للموبايل */}\r\n      <button\r\n        className=\"md:hidden text-white\"\r\n        onClick={toggleMenu}\r\n        aria-label=\"Toggle menu\"\r\n      >\r\n        {menuOpen ? <X size={28} /> : <Menu size={28} />}\r\n      </button>\r\n    </div>\r\n  </div>\r\n\r\n  {/* القائمة الجانبية للموبايل */}\r\n  {menuOpen && (\r\n    <div className=\"md:hidden flex flex-col px-6 pb-4 pt-2 space-y-4 bg-gray-800/30 text-white backdrop-blur-md border-t border-white/10\">\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/\" onClick={toggleMenu}>Home</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/about\" onClick={toggleMenu}>About us</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/committees\" onClick={toggleMenu}>Committees</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/call-for-papers\" onClick={toggleMenu}>Call For Papers</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/submission\" onClick={toggleMenu}>Submission</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/registration\" onClick={toggleMenu}>Registration</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/venue\" onClick={toggleMenu}>Venue</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/expo\">IREGO Expo</Link>{/* Corrected href */}\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/the-best\">IREGO The Best</Link>\r\n    \r\n      \r\n      <Link\r\n        href=\"/contact-us\"\r\n        scroll={true}\r\n        onClick={toggleMenu}\r\n        className=\"block bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md text-sm font-semibold w-fit\"\r\n      >\r\n        Contact Us\r\n      </Link>\r\n    </div>\r\n  )}\r\n</nav>\r\n    \r\n    </>\r\n  );\r\n}\r\nexport default Navbar ; "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA,oOAAkD,qBAAqB;AACvE;;;AAPA;;;;;;;AASC,SAAS;;IACR,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,aAAa,IAAM,YAAY,CAAC;IAEtC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,SAAS,mBAAmB,KAAiB;gBAC3C,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;oBAC9E,gBAAgB;gBAClB;YACF;YACA,IAAI,cAAc;gBAChB,SAAS,gBAAgB,CAAC,aAAa;YACzC,OAAO;gBACL,SAAS,mBAAmB,CAAC,aAAa;YAC5C;YACA;oCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;2BAAG;QAAC;KAAa;IAEjB,MAAM,eAAe;QACnB,IAAI;YACF,MAAM;YACN,gBAAgB;YAChB,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,0CAA0C;YAC1C,gBAAgB;YAChB,OAAO,IAAI,CAAC;QACd;IACF;IAEA,qBACE;kBACE,cAAA,6LAAC;YAAI,WAAU;;8BACnB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCAAC,KAAI;gCAAY,KAAI;gCAAO,OAAO;gCAAI,QAAQ;;;;;;;;;;;sCAI3D,6LAAC;4BAAG,WAAU;;8CAEZ,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAmC;;;;;;;;;;;8CAK9D,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAmC;;;;;;;;;;;8CAKnE,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAc,WAAU;kDAAmC;;;;;;;;;;;8CAKxE,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAmB,WAAU;kDAAmC;;;;;;;;;;;8CAK7E,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAc,WAAU;kDAAmC;;;;;;;;;;;8CAKxE,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAgB,WAAU;kDAAmC;;;;;;;;;;;8CAK1E,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAmC;;;;;;;;;;;8CAKnE,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAQ,WAAU;kDAAmC;;;;;;;;;;;8CAKlE,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAAmC;;;;;;;;;;;8CAKtE,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,QAAQ;wCACR,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAQD,6LAAC;4BAAI,WAAU;;gCACZ,qBACC;;sDACE,6LAAC;4CAAO,SAAS,IAAM,gBAAgB,CAAC;4CAAe,WAAU;sDAC/D,cAAA,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAK,KAAK,QAAQ,IAAI;gDACtB,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;wCAIb,8BACC,6LAAC;4CACC,KAAK;4CACL,WAAU;sDAEV,cAAA,6LAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;iEAOP,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAMH,6LAAC;oCACC,WAAU;oCACV,SAAS;oCACT,cAAW;8CAEV,yBAAW,6LAAC,+LAAA,CAAA,IAAC;wCAAC,MAAM;;;;;6DAAS,6LAAC,qMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;gBAM/C,0BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAI,SAAS;sCAAY;;;;;;sCACjF,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAS,SAAS;sCAAY;;;;;;sCACtF,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAc,SAAS;sCAAY;;;;;;sCAC3F,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAmB,SAAS;sCAAY;;;;;;sCAChG,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAc,SAAS;sCAAY;;;;;;sCAC3F,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAgB,SAAS;sCAAY;;;;;;sCAC7F,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAS,SAAS;sCAAY;;;;;;sCACtF,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;sCAAQ;;;;;;sCAChE,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;sCAAY;;;;;;sCAGpE,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,QAAQ;4BACR,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;AASP;GAnMU;;QAKiB,iIAAA,CAAA,UAAO;QACjB,qIAAA,CAAA,YAAS;;;KANhB;uCAoMK", "debugId": null}}]}