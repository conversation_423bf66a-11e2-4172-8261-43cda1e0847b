{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/context/UserContext.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { createContext, useContext, useState, ReactNode } from \"react\";\r\n\r\ntype User = {\r\n  name?: string;\r\n  email?: string;\r\n  photoURL?: string;\r\n};\r\n\r\ntype UserContextType = {\r\n  user: User | null;\r\n  setUser: (user: User | null) => void;\r\n};\r\n\r\nconst UserContext = createContext<UserContextType | undefined>(undefined);\r\n\r\nexport function UserProvider({ children }: { children: ReactNode }) {\r\n  const [user, setUser] = useState<User | null>(null);\r\n\r\n  return (\r\n    <UserContext.Provider value={{ user, setUser }}>\r\n      {children}\r\n    </UserContext.Provider>\r\n  );\r\n}\r\n\r\nexport function useUser() {\r\n  const context = useContext(UserContext);\r\n  if (!context) {\r\n    throw new Error(\"useUser must be used within a UserProvider\");\r\n  }\r\n  return context;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAeA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAA2B;;IAChE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAE9C,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;YAAE;YAAM;QAAQ;kBAC1C;;;;;;AAGP;GARgB;KAAA;AAUT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/components/SafeImage.tsx"], "sourcesContent": ["\"use client\";\n\nimport Image from 'next/image';\nimport { useState } from 'react';\n\ninterface SafeImageProps {\n  src: string;\n  alt: string;\n  width: number;\n  height: number;\n  className?: string;\n  fallbackSrc?: string;\n  priority?: boolean;\n  suppressHydrationWarning?: boolean;\n}\n\nexport default function SafeImage({ \n  src, \n  alt, \n  width, \n  height, \n  className, \n  fallbackSrc = '/logo.png', \n  priority = false,\n  suppressHydrationWarning = false\n}: SafeImageProps) {\n  const [imgSrc, setImgSrc] = useState(src);\n  const [hasError, setHasError] = useState(false);\n\n  const handleError = () => {\n    if (!hasError) {\n      setHasError(true);\n      setImgSrc(fallbackSrc);\n    }\n  };\n\n  return (\n    <Image\n      src={imgSrc}\n      alt={alt}\n      width={width}\n      height={height}\n      className={className}\n      priority={priority}\n      suppressHydrationWarning={suppressHydrationWarning}\n      onError={handleError}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAgBe,SAAS,UAAU,EAChC,GAAG,EACH,GAAG,EACH,KAAK,EACL,MAAM,EACN,SAAS,EACT,cAAc,WAAW,EACzB,WAAW,KAAK,EAChB,2BAA2B,KAAK,EACjB;;IACf,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,cAAc;QAClB,IAAI,CAAC,UAAU;YACb,YAAY;YACZ,UAAU;QACZ;IACF;IAEA,qBACE,6LAAC,gIAAA,CAAA,UAAK;QACJ,KAAK;QACL,KAAK;QACL,OAAO;QACP,QAAQ;QACR,WAAW;QACX,UAAU;QACV,0BAA0B;QAC1B,SAAS;;;;;;AAGf;GAhCwB;KAAA", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/components/Navbar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport Image from \"next/image\";\r\nimport { Menu, X } from \"lucide-react\";\r\nimport { useUser } from \"../context/UserContext\"; // عدل حسب مكان الملف\r\nimport { useRouter } from \"next/navigation\";\r\nimport SafeImage from \"./SafeImage\";\r\n\r\n function Navbar() {\r\n  const [menuOpen, setMenuOpen] = useState(false);\r\n  const [dropdownOpen, setDropdownOpen] = useState(false);\r\n  const toggleMenu = () => setMenuOpen(!menuOpen);\r\n\r\n  const { user, setUser } = useUser();\r\n  const router = useRouter();\r\n\r\n  const dropdownRef = useRef<HTMLDivElement>(null);\r\n\r\n  // إغلاق الـ dropdown عند الضغط خارجها\r\n  useEffect(() => {\r\n    function handleClickOutside(event: MouseEvent) {\r\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\r\n        setDropdownOpen(false);\r\n      }\r\n    }\r\n    if (dropdownOpen) {\r\n      document.addEventListener(\"mousedown\", handleClickOutside);\r\n    } else {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    }\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, [dropdownOpen]);\r\n\r\n  const handleLogout = () => {\r\n    setUser(null); // مسح بيانات المستخدم (تأكد من تنفيذ هذه الوظيفة في UserContext)\r\n    setDropdownOpen(false);\r\n    router.push(\"/login\"); // توجيه لصفحة تسجيل الدخول\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <nav className=\"absolute top-[35px] left-0 w-full bg-gray-800/30 text-white z-40 backdrop-blur-md border-b border-white/10\">\r\n  <div className=\"max-w-7xl mx-auto px-4 py-3 flex items-center justify-between h-16\">\r\n    {/* الشعار */}\r\n    <div className=\"flex items-center space-x-2 min-w-[60px]\">\r\n      <Image src=\"/logo.png\" alt=\"Logo\" width={60} height={40} priority suppressHydrationWarning />\r\n    </div>\r\n\r\n    {/* روابط سطح المكتب */}\r\n<ul className=\"hidden md:flex items-center gap-3  text-sm uppercase font-medium\">\r\n\r\n  <li>\r\n    <Link href=\"/\" className=\"hover:text-orange-400 transition\">\r\n      Home\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/about\" className=\"hover:text-orange-400 transition\">\r\n      About\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/committees\" className=\"hover:text-orange-400 transition\">\r\n      Committees\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/call-for-papers\" className=\"hover:text-orange-400 transition\">\r\n      Call  Papers\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/submission\" className=\"hover:text-orange-400 transition\">\r\n      Submission\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/registration\" className=\"hover:text-orange-400 transition\">\r\n      Registration\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/venue\" className=\"hover:text-orange-400 transition\">\r\n      Venue\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/expo\" className=\"hover:text-orange-400 transition\">\r\n     Expo\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/the-best\" className=\"hover:text-orange-400 transition\">\r\n        Best\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link\r\n      href=\"/contact-us\"\r\n      scroll={true}\r\n      onClick={toggleMenu}\r\n      className=\"bg-orange-500 hover:bg-orange-600 text-white px-4 py-1.5 rounded-md text-xs font-semibold\"\r\n    >\r\n      Contact \r\n    </Link>\r\n  </li>\r\n</ul>\r\n\r\n\r\n    {/* صورة المستخدم أو زر تسجيل الدخول + زر القائمة للموبايل */}\r\n    <div className=\"flex items-center space-x-3 min-w-[100px] justify-end relative\">\r\n      {user ? (\r\n        <>\r\n          <button onClick={() => setDropdownOpen(!dropdownOpen)} className=\"focus:outline-none\">\r\n            <SafeImage\r\n              src={user.photoURL || \"/logo.png\"}\r\n              alt=\"User\"\r\n              width={36}\r\n              height={36}\r\n              className=\"rounded-full border border-white\"\r\n              fallbackSrc=\"/logo.png\"\r\n              suppressHydrationWarning\r\n            />\r\n          </button>\r\n\r\n          {dropdownOpen && (\r\n            <div\r\n              ref={dropdownRef}\r\n              className=\"absolute right-0 mt-2 w-40 bg-gray-900 border border-gray-700 rounded shadow-lg z-50\"\r\n            >\r\n              <button\r\n                onClick={handleLogout}\r\n                className=\"block w-full text-left px-4 py-2 text-sm hover:bg-orange-600 hover:text-white text-red-500\"\r\n              >\r\n                تسجيل الخروج\r\n              </button>\r\n            </div>\r\n          )}\r\n        </>\r\n      ) : (\r\n        <Link\r\n          href=\"/login\"\r\n          className=\"text-sm bg-orange-500 hover:bg-orange-600 px-3 py-1 rounded text-white\"\r\n        >\r\n          Login\r\n        </Link>\r\n      )}\r\n\r\n      {/* زر القائمة للموبايل */}\r\n      <button\r\n        className=\"md:hidden text-white\"\r\n        onClick={toggleMenu}\r\n        aria-label=\"Toggle menu\"\r\n      >\r\n        {menuOpen ? <X size={28} /> : <Menu size={28} />}\r\n      </button>\r\n    </div>\r\n  </div>\r\n\r\n  {/* القائمة الجانبية للموبايل */}\r\n  {menuOpen && (\r\n    <div className=\"md:hidden flex flex-col px-6 pb-4 pt-2 space-y-4 bg-gray-800/30 text-white backdrop-blur-md border-t border-white/10\">\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/\" onClick={toggleMenu}>Home</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/about\" onClick={toggleMenu}>About us</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/committees\" onClick={toggleMenu}>Committees</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/call-for-papers\" onClick={toggleMenu}>Call For Papers</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/submission\" onClick={toggleMenu}>Submission</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/registration\" onClick={toggleMenu}>Registration</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/venue\" onClick={toggleMenu}>Venue</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/expo\">IREGO Expo</Link>{/* Corrected href */}\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/the-best\">IREGO The Best</Link>\r\n    \r\n      \r\n      <Link\r\n        href=\"/contact-us\"\r\n        scroll={true}\r\n        onClick={toggleMenu}\r\n        className=\"block bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md text-sm font-semibold w-fit\"\r\n      >\r\n        Contact Us\r\n      </Link>\r\n    </div>\r\n  )}\r\n</nav>\r\n    \r\n    </>\r\n  );\r\n}\r\nexport default Navbar ; "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA,oOAAkD,qBAAqB;AACvE;AACA;;;AARA;;;;;;;;AAUC,SAAS;;IACR,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,aAAa,IAAM,YAAY,CAAC;IAEtC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,SAAS,mBAAmB,KAAiB;gBAC3C,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;oBAC9E,gBAAgB;gBAClB;YACF;YACA,IAAI,cAAc;gBAChB,SAAS,gBAAgB,CAAC,aAAa;YACzC,OAAO;gBACL,SAAS,mBAAmB,CAAC,aAAa;YAC5C;YACA;oCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;2BAAG;QAAC;KAAa;IAEjB,MAAM,eAAe;QACnB,QAAQ,OAAO,iEAAiE;QAChF,gBAAgB;QAChB,OAAO,IAAI,CAAC,WAAW,2BAA2B;IACpD;IAEA,qBACE;kBACE,cAAA,6LAAC;YAAI,WAAU;;8BACnB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCAAC,KAAI;gCAAY,KAAI;gCAAO,OAAO;gCAAI,QAAQ;gCAAI,QAAQ;gCAAC,wBAAwB;;;;;;;;;;;sCAIhG,6LAAC;4BAAG,WAAU;;8CAEZ,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAmC;;;;;;;;;;;8CAK9D,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAmC;;;;;;;;;;;8CAKnE,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAc,WAAU;kDAAmC;;;;;;;;;;;8CAKxE,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAmB,WAAU;kDAAmC;;;;;;;;;;;8CAK7E,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAc,WAAU;kDAAmC;;;;;;;;;;;8CAKxE,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAgB,WAAU;kDAAmC;;;;;;;;;;;8CAK1E,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAmC;;;;;;;;;;;8CAKnE,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAQ,WAAU;kDAAmC;;;;;;;;;;;8CAKlE,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAAmC;;;;;;;;;;;8CAKtE,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,QAAQ;wCACR,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAQD,6LAAC;4BAAI,WAAU;;gCACZ,qBACC;;sDACE,6LAAC;4CAAO,SAAS,IAAM,gBAAgB,CAAC;4CAAe,WAAU;sDAC/D,cAAA,6LAAC,kIAAA,CAAA,UAAS;gDACR,KAAK,KAAK,QAAQ,IAAI;gDACtB,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;gDACV,aAAY;gDACZ,wBAAwB;;;;;;;;;;;wCAI3B,8BACC,6LAAC;4CACC,KAAK;4CACL,WAAU;sDAEV,cAAA,6LAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;iEAOP,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAMH,6LAAC;oCACC,WAAU;oCACV,SAAS;oCACT,cAAW;8CAEV,yBAAW,6LAAC,+LAAA,CAAA,IAAC;wCAAC,MAAM;;;;;6DAAS,6LAAC,qMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;gBAM/C,0BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAI,SAAS;sCAAY;;;;;;sCACjF,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAS,SAAS;sCAAY;;;;;;sCACtF,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAc,SAAS;sCAAY;;;;;;sCAC3F,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAmB,SAAS;sCAAY;;;;;;sCAChG,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAc,SAAS;sCAAY;;;;;;sCAC3F,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAgB,SAAS;sCAAY;;;;;;sCAC7F,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAS,SAAS;sCAAY;;;;;;sCACtF,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;sCAAQ;;;;;;sCAChE,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;sCAAY;;;;;;sCAGpE,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,QAAQ;4BACR,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;AASP;GA9LU;;QAKkB,iIAAA,CAAA,UAAO;QAClB,qIAAA,CAAA,YAAS;;;KANhB;uCA+LK", "debugId": null}}, {"offset": {"line": 570, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/components/ErrorBoundary.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\n\ninterface ErrorBoundaryState {\n  hasError: boolean;\n  error?: Error;\n}\n\ninterface ErrorBoundaryProps {\n  children: React.ReactNode;\n  fallback?: React.ComponentType<{ error: Error; resetError: () => void }>;\n}\n\nclass ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {\n  constructor(props: ErrorBoundaryProps) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError(error: Error): ErrorBoundaryState {\n    return { hasError: true, error };\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('Error caught by boundary:', error, errorInfo);\n  }\n\n  resetError = () => {\n    this.setState({ hasError: false, error: undefined });\n  };\n\n  render() {\n    if (this.state.hasError) {\n      if (this.props.fallback) {\n        const FallbackComponent = this.props.fallback;\n        return <FallbackComponent error={this.state.error!} resetError={this.resetError} />;\n      }\n\n      return (\n        <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n          <div className=\"max-w-md w-full bg-white rounded-lg shadow-md p-6 text-center\">\n            <div className=\"text-red-500 text-6xl mb-4\">⚠️</div>\n            <h2 className=\"text-2xl font-bold text-gray-800 mb-2\">Something went wrong</h2>\n            <p className=\"text-gray-600 mb-4\">\n              We're sorry, but something unexpected happened. Please try refreshing the page.\n            </p>\n            <button\n              onClick={this.resetError}\n              className=\"bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded font-semibold\"\n            >\n              Try Again\n            </button>\n            <button\n              onClick={() => window.location.reload()}\n              className=\"ml-2 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded font-semibold\"\n            >\n              Refresh Page\n            </button>\n          </div>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nexport default ErrorBoundary;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAcA,MAAM,sBAAsB,6JAAA,CAAA,UAAK,CAAC,SAAS;IACzC,YAAY,KAAyB,CAAE;QACrC,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,yBAAyB,KAAY,EAAsB;QAChE,OAAO;YAAE,UAAU;YAAM;QAAM;IACjC;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,6BAA6B,OAAO;IACpD;IAEA,aAAa;QACX,IAAI,CAAC,QAAQ,CAAC;YAAE,UAAU;YAAO,OAAO;QAAU;IACpD,EAAE;IAEF,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,MAAM,oBAAoB,IAAI,CAAC,KAAK,CAAC,QAAQ;gBAC7C,qBAAO,6LAAC;oBAAkB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;oBAAG,YAAY,IAAI,CAAC,UAAU;;;;;;YACjF;YAEA,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAA6B;;;;;;sCAC5C,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,6LAAC;4BACC,SAAS,IAAI,CAAC,UAAU;4BACxB,WAAU;sCACX;;;;;;sCAGD,6LAAC;4BACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;4BACrC,WAAU;sCACX;;;;;;;;;;;;;;;;;QAMT;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 685, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/components/ChatbotPopup.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useRef, useEffect } from 'react';\r\n\r\nconst ChatbotPopup = () => {\r\n  const [showChat, setShowChat] = useState(false);\r\n  const [messages, setMessages] = useState([\r\n    {\r\n      type: 'bot',\r\n      text: '🤖 مرحباً! أنا مساعدك الذكي المتطور! 🧠\\n\\n✨ يمكنني مساعدتك في:\\n🎯 معلومات المؤتمر الشاملة\\n⚡ الطاقة المتجددة والتكنولوجيا\\n🌍 تغير المناخ والبيئة\\n📚 البحث العلمي والأكاديمي\\n💡 أي سؤال علمي أو تقني\\n\\nاسألني أي شيء وسأعطيك إجابة مفصلة وذكية! 😊'\r\n    }\r\n  ]);\r\n  const [inputText, setInputText] = useState('');\r\n  const [isTyping, setIsTyping] = useState(false);\r\n  const messagesEndRef = useRef(null);\r\n\r\n  const scrollToBottom = () => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\r\n  };\r\n\r\n  useEffect(() => {\r\n    scrollToBottom();\r\n  }, [messages]);\r\n\r\n  // 🧠 نظام الذكاء الاصطناعي المتقدم\r\n  const [conversationHistory, setConversationHistory] = useState([]);\r\n  const [userProfile, setUserProfile] = useState({});\r\n  const [learningData, setLearningData] = useState({});\r\n\r\n  // قاعدة المعرفة الشاملة\r\n  const knowledgeBase = {\r\n    // معلومات المؤتمر\r\n    conference: {\r\n      name: 'مؤتمر الطاقة المتجددة والنفط والغاز وتغير المناخ',\r\n      date: '25-27 نوفمبر 2025',\r\n      location: 'طرابلس، ليبيا',\r\n      topics: ['الطاقة المتجددة', 'النفط والغاز', 'تغير المناخ', 'التنمية المستدامة'],\r\n      languages: ['العربية', 'الإنجليزية'],\r\n      deadlines: {\r\n        abstract: '15 يونيو 2025',\r\n        fullPaper: '1 أكتوبر 2025',\r\n        notification: '15 أغسطس 2025'\r\n      }\r\n    },\r\n    // معلومات علمية\r\n    science: {\r\n      renewableEnergy: {\r\n        solar: 'الطاقة الشمسية تحول ضوء الشمس إلى كهرباء باستخدام الخلايا الكهروضوئية',\r\n        wind: 'طاقة الرياح تستخدم التوربينات لتحويل حركة الهواء إلى طاقة كهربائية',\r\n        hydro: 'الطاقة المائية تستغل تدفق المياه لتوليد الكهرباء',\r\n        geothermal: 'الطاقة الحرارية الأرضية تستخدم حرارة باطن الأرض'\r\n      },\r\n      climateChange: {\r\n        causes: 'أسباب تغير المناخ تشمل انبعاثات غازات الدفيئة والأنشطة البشرية',\r\n        effects: 'تأثيرات تغير المناخ تشمل ارتفاع درجات الحرارة وذوبان الأنهار الجليدية',\r\n        solutions: 'الحلول تشمل الطاقة المتجددة وتقليل الانبعاثات والتشجير'\r\n      }\r\n    },\r\n    // معلومات عامة\r\n    general: {\r\n      technology: 'التكنولوجيا تتطور بسرعة وتؤثر على جميع جوانب الحياة',\r\n      education: 'التعليم هو أساس التقدم والتنمية في أي مجتمع',\r\n      health: 'الصحة هي أهم ما يملكه الإنسان ويجب الاهتمام بها',\r\n      economy: 'الاقتصاد يعتمد على الموارد والاستثمار والابتكار'\r\n    }\r\n  };\r\n\r\n  // 🤖 نظام الذكاء الاصطناعي المتطور\r\n  const analyzeQuestion = (question) => {\r\n    const lowerQ = question.toLowerCase();\r\n\r\n    // تحليل نوع السؤال\r\n    const questionTypes = {\r\n      what: ['ما', 'ماذا', 'what'],\r\n      when: ['متى', 'when', 'تاريخ', 'موعد'],\r\n      where: ['أين', 'where', 'مكان'],\r\n      how: ['كيف', 'how', 'طريقة'],\r\n      why: ['لماذا', 'why', 'سبب'],\r\n      who: ['من', 'who', 'مين']\r\n    };\r\n\r\n    let questionType = 'general';\r\n    for (const [type, keywords] of Object.entries(questionTypes)) {\r\n      if (keywords.some(keyword => lowerQ.includes(keyword))) {\r\n        questionType = type;\r\n        break;\r\n      }\r\n    }\r\n\r\n    return { questionType, originalQuestion: question, processedQuestion: lowerQ };\r\n  };\r\n\r\n  const generateIntelligentResponse = (analysis) => {\r\n    const { questionType, processedQuestion } = analysis;\r\n\r\n    // البحث في قاعدة المعرفة\r\n    if (processedQuestion.includes('مؤتمر') || processedQuestion.includes('conference')) {\r\n      if (questionType === 'when') {\r\n        return `📅 المؤتمر سيقام في ${knowledgeBase.conference.date} في ${knowledgeBase.conference.location}. هذا موعد مهم جداً لا تفوته!`;\r\n      }\r\n      if (questionType === 'where') {\r\n        return `📍 سيقام المؤتمر في ${knowledgeBase.conference.location}. مدينة طرابلس الجميلة ستستضيف هذا الحدث العلمي المهم.`;\r\n      }\r\n      if (questionType === 'what') {\r\n        return `🎯 ${knowledgeBase.conference.name} هو حدث علمي عالمي يجمع الخبراء والباحثين لمناقشة أحدث التطورات في مجال الطاقة والبيئة.`;\r\n      }\r\n    }\r\n\r\n    // الطاقة المتجددة\r\n    if (processedQuestion.includes('طاقة') || processedQuestion.includes('energy')) {\r\n      if (processedQuestion.includes('شمسية') || processedQuestion.includes('solar')) {\r\n        return `☀️ ${knowledgeBase.science.renewableEnergy.solar}. هذه التقنية تشهد تطوراً مذهلاً ويمكنها تلبية احتياجات العالم من الطاقة!`;\r\n      }\r\n      if (processedQuestion.includes('رياح') || processedQuestion.includes('wind')) {\r\n        return `💨 ${knowledgeBase.science.renewableEnergy.wind}. الرياح مصدر طاقة لا ينضب ونظيف تماماً!`;\r\n      }\r\n      return `⚡ الطاقة المتجددة هي مستقبل البشرية! تشمل الطاقة الشمسية وطاقة الرياح والمائية والحرارية الأرضية. كلها مصادر نظيفة ومستدامة.`;\r\n    }\r\n\r\n    // تغير المناخ\r\n    if (processedQuestion.includes('مناخ') || processedQuestion.includes('climate')) {\r\n      if (questionType === 'why') {\r\n        return `🌍 ${knowledgeBase.science.climateChange.causes}. هذه مشكلة عالمية تتطلب تضافر جهود الجميع لحلها.`;\r\n      }\r\n      return `🌡️ تغير المناخ قضية حرجة تؤثر على كوكبنا. ${knowledgeBase.science.climateChange.effects}. لكن هناك حلول! ${knowledgeBase.science.climateChange.solutions}`;\r\n    }\r\n\r\n    // أسئلة التسجيل\r\n    if (processedQuestion.includes('تسجيل') || processedQuestion.includes('اشتراك')) {\r\n      return `📝 يمكنك التسجيل بسهولة من خلال موقعنا! اضغط على زر التسجيل وستجد جميع التفاصيل. التسجيل المبكر له مزايا خاصة!`;\r\n    }\r\n\r\n    // أسئلة الأوراق البحثية\r\n    if (processedQuestion.includes('بحث') || processedQuestion.includes('ورقة') || processedQuestion.includes('paper')) {\r\n      return `📚 مواعيد مهمة للباحثين:\r\n      📅 آخر موعد للملخصات: ${knowledgeBase.conference.deadlines.abstract}\r\n      📅 إشعار القبول: ${knowledgeBase.conference.deadlines.notification}\r\n      📅 الأوراق النهائية: ${knowledgeBase.conference.deadlines.fullPaper}\r\n      لا تفوت هذه المواعيد!`;\r\n    }\r\n\r\n    // أسئلة عامة ذكية\r\n    if (processedQuestion.includes('تكنولوجيا') || processedQuestion.includes('technology')) {\r\n      return `💻 ${knowledgeBase.general.technology}. في مؤتمرنا ستتعرف على أحدث التقنيات في مجال الطاقة!`;\r\n    }\r\n\r\n    if (processedQuestion.includes('تعليم') || processedQuestion.includes('education')) {\r\n      return `🎓 ${knowledgeBase.general.education}. مؤتمرنا فرصة تعليمية رائعة للتعلم من الخبراء العالميين!`;\r\n    }\r\n\r\n    // ردود ذكية للتحيات\r\n    if (processedQuestion.includes('مرحبا') || processedQuestion.includes('hello') || processedQuestion.includes('hi')) {\r\n      const greetings = [\r\n        '👋 أهلاً وسهلاً! سعيد جداً بلقائك!',\r\n        '🌟 مرحباً بك! كيف يمكنني مساعدتك اليوم؟',\r\n        '😊 أهلاً! أنا هنا لأجيب على جميع أسئلتك!'\r\n      ];\r\n      return greetings[Math.floor(Math.random() * greetings.length)];\r\n    }\r\n\r\n    if (processedQuestion.includes('شكرا') || processedQuestion.includes('thanks')) {\r\n      return '🙏 العفو! سعيد جداً لمساعدتك. هل تحتاج لأي معلومات أخرى؟';\r\n    }\r\n\r\n    // رد ذكي للأسئلة غير المفهومة\r\n    return `🤔 سؤال مثير للاهتمام! دعني أفكر... يمكنني مساعدتك في:\r\n    🎯 معلومات المؤتمر (التاريخ، المكان، المواضيع)\r\n    📝 التسجيل والاشتراك\r\n    📚 الأوراق البحثية والمواعيد\r\n    ⚡ الطاقة المتجددة والتكنولوجيا\r\n    🌍 تغير المناخ والبيئة\r\n    💡 أي موضوع علمي أو تقني آخر!\r\n\r\n    أعد صياغة سؤالك وسأعطيك إجابة مفصلة! 😊`;\r\n  };\r\n\r\n  const findAnswer = (question) => {\r\n    // حفظ السؤال في تاريخ المحادثة للتعلم\r\n    setConversationHistory(prev => [...prev, question]);\r\n\r\n    // تحليل السؤال\r\n    const analysis = analyzeQuestion(question);\r\n\r\n    // توليد رد ذكي\r\n    const response = generateIntelligentResponse(analysis);\r\n\r\n    // تحديث بيانات التعلم\r\n    setLearningData(prev => ({\r\n      ...prev,\r\n      [question]: response,\r\n      totalQuestions: (prev.totalQuestions || 0) + 1\r\n    }));\r\n\r\n    return response;\r\n  };\r\n\r\n  // 💡 اقتراحات ذكية للأسئلة\r\n  const suggestQuestions = () => {\r\n    const suggestions = [\r\n      '📅 متى موعد المؤتمر؟',\r\n      '📍 أين سيقام المؤتمر؟',\r\n      '📝 كيف أسجل في المؤتمر؟',\r\n      '⚡ ما هي الطاقة المتجددة؟',\r\n      '🌍 ما أسباب تغير المناخ؟',\r\n      '📚 متى آخر موعد للأوراق البحثية؟',\r\n      '🎯 ما مواضيع المؤتمر؟',\r\n      '🏆 هل سأحصل على شهادة؟'\r\n    ];\r\n    return suggestions[Math.floor(Math.random() * suggestions.length)];\r\n  };\r\n\r\n  const handleSendMessage = () => {\r\n    if (!inputText.trim()) return;\r\n\r\n    const userMessage = { type: 'user', text: inputText };\r\n    setMessages(prev => [...prev, userMessage]);\r\n\r\n    // تحديث ملف المستخدم\r\n    setUserProfile(prev => ({\r\n      ...prev,\r\n      totalQuestions: (prev.totalQuestions || 0) + 1,\r\n      lastQuestion: inputText,\r\n      timestamp: new Date().toISOString()\r\n    }));\r\n\r\n    setInputText('');\r\n    setIsTyping(true);\r\n\r\n    // محاكاة تأخير الرد مع ذكاء متقدم\r\n    setTimeout(() => {\r\n      const botResponse = findAnswer(inputText);\r\n      const suggestion = suggestQuestions();\r\n\r\n      const enhancedResponse = `${botResponse}\\n\\n💡 اقتراح: ${suggestion}`;\r\n\r\n      const botMessage = { type: 'bot', text: enhancedResponse };\r\n      setMessages(prev => [...prev, botMessage]);\r\n      setIsTyping(false);\r\n    }, Math.random() * 1500 + 500); // تأخير عشوائي لمحاكاة التفكير\r\n  };\r\n\r\n  const handleKeyDown = (e) => {\r\n    if (e.key === 'Enter') {\r\n      handleSendMessage();\r\n    }\r\n  };\r\n\r\n  const toggleChat = () => {\r\n    setShowChat(!showChat);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {/* زر ثابت في الركن السفلي الأيمن */}\r\n      <button\r\n        style={{\r\n          position: 'fixed',\r\n          bottom: '20px',\r\n          right: '20px',\r\n          zIndex: 1000,\r\n          padding: '15px 20px',\r\n          background: 'linear-gradient(45deg, #007bff, #0056b3)',\r\n          color: 'white',\r\n          border: 'none',\r\n          borderRadius: '50px',\r\n          cursor: 'pointer',\r\n          boxShadow: '0 4px 15px rgba(0,123,255,0.3)',\r\n          fontSize: '16px',\r\n          fontWeight: 'bold',\r\n          transition: 'all 0.3s ease',\r\n          transform: showChat ? 'scale(0.9)' : 'scale(1)',\r\n        }}\r\n        onClick={toggleChat}\r\n        onMouseEnter={(e) => {\r\n          e.target.style.transform = 'scale(1.1)';\r\n          e.target.style.boxShadow = '0 6px 20px rgba(0,123,255,0.4)';\r\n        }}\r\n        onMouseLeave={(e) => {\r\n          e.target.style.transform = showChat ? 'scale(0.9)' : 'scale(1)';\r\n          e.target.style.boxShadow = '0 4px 15px rgba(0,123,255,0.3)';\r\n        }}\r\n      >\r\n        {showChat ? '✕' : '💬 Chat'}\r\n      </button>\r\n\r\n      {/* الصفحة اللي بتظهر عند الضغط */}\r\n      {showChat && (\r\n        <div\r\n          style={{\r\n            position: 'fixed',\r\n            bottom: '70px',\r\n            right: '20px',\r\n            width: '300px',\r\n            height: '400px',\r\n            backgroundColor: 'white',\r\n            boxShadow: '0 4px 8px rgba(0,0,0,0.2)',\r\n            borderRadius: '10px',\r\n            zIndex: 999,\r\n            display: 'flex',\r\n            flexDirection: 'column',\r\n          }}\r\n        >\r\n          {/* رأس الـ Chatbot */}\r\n          <div\r\n            style={{\r\n              backgroundColor: '#007bff',\r\n              color: 'white',\r\n              padding: '10px',\r\n              borderTopLeftRadius: '10px',\r\n              borderTopRightRadius: '10px',\r\n            }}\r\n          >\r\n            <h4 style={{ margin: 0, display: 'flex', alignItems: 'center', gap: '8px' }}>\r\n              🧠 مساعد ذكي متطور\r\n              <span style={{ fontSize: '12px', opacity: 0.8 }}>• متصل</span>\r\n            </h4>\r\n          </div>\r\n          \r\n          {/* محتوى الـ Chatbot */}\r\n          <div style={{ flex: 1, padding: '10px', overflowY: 'auto', display: 'flex', flexDirection: 'column' }}>\r\n            {/* الرسائل */}\r\n            <div style={{ flex: 1, overflowY: 'auto', marginBottom: '10px' }}>\r\n              {messages.map((message, index) => (\r\n                <div\r\n                  key={index}\r\n                  style={{\r\n                    marginBottom: '10px',\r\n                    display: 'flex',\r\n                    justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start'\r\n                  }}\r\n                >\r\n                  <div\r\n                    style={{\r\n                      maxWidth: '85%',\r\n                      padding: '10px 15px',\r\n                      borderRadius: '18px',\r\n                      backgroundColor: message.type === 'user'\r\n                        ? 'linear-gradient(45deg, #007bff, #0056b3)'\r\n                        : 'linear-gradient(45deg, #f8f9fa, #e9ecef)',\r\n                      color: message.type === 'user' ? 'white' : '#333',\r\n                      fontSize: '14px',\r\n                      lineHeight: '1.5',\r\n                      boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\r\n                      border: message.type === 'bot' ? '1px solid #dee2e6' : 'none',\r\n                      whiteSpace: 'pre-line'\r\n                    }}\r\n                  >\r\n                    {message.text}\r\n                  </div>\r\n                </div>\r\n              ))}\r\n              {isTyping && (\r\n                <div style={{ display: 'flex', justifyContent: 'flex-start', marginBottom: '10px' }}>\r\n                  <div\r\n                    style={{\r\n                      padding: '10px 15px',\r\n                      borderRadius: '18px',\r\n                      backgroundColor: 'linear-gradient(45deg, #f8f9fa, #e9ecef)',\r\n                      fontSize: '14px',\r\n                      border: '1px solid #dee2e6',\r\n                      boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\r\n                      animation: 'pulse 1.5s infinite'\r\n                    }}\r\n                  >\r\n                    🤖 أفكر في إجابة ذكية...\r\n                  </div>\r\n                </div>\r\n              )}\r\n              <div ref={messagesEndRef} />\r\n            </div>\r\n\r\n            {/* مربع الإدخال */}\r\n            <div style={{ display: 'flex', gap: '5px' }}>\r\n              <input\r\n                type=\"text\"\r\n                value={inputText}\r\n                onChange={(e) => setInputText(e.target.value)}\r\n                onKeyDown={handleKeyDown}\r\n                placeholder=\"اكتب رسالتك هنا...\"\r\n                style={{\r\n                  flex: 1,\r\n                  padding: '8px',\r\n                  border: '1px solid #ddd',\r\n                  borderRadius: '20px',\r\n                  outline: 'none',\r\n                  fontSize: '14px'\r\n                }}\r\n              />\r\n              <button\r\n                onClick={handleSendMessage}\r\n                style={{\r\n                  padding: '8px 15px',\r\n                  backgroundColor: '#007bff',\r\n                  color: 'white',\r\n                  border: 'none',\r\n                  borderRadius: '20px',\r\n                  cursor: 'pointer',\r\n                  fontSize: '14px'\r\n                }}\r\n              >\r\n                إرسال\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* زر إغلاق */}\r\n          <button\r\n            style={{\r\n              position: 'absolute',\r\n              top: '5px',\r\n              right: '10px',\r\n              background: 'transparent',\r\n              border: 'none',\r\n              fontSize: '20px',\r\n              cursor: 'pointer',\r\n            }}\r\n            onClick={toggleChat}\r\n          >\r\n            &times;\r\n          </button>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ChatbotPopup;"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIA,MAAM,eAAe;;IACnB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC;YACE,MAAM;YACN,MAAM;QACR;KACD;IACD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE9B,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG;QAAC;KAAS;IAEb,mCAAmC;IACnC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACjE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAChD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAElD,wBAAwB;IACxB,MAAM,gBAAgB;QACpB,kBAAkB;QAClB,YAAY;YACV,MAAM;YACN,MAAM;YACN,UAAU;YACV,QAAQ;gBAAC;gBAAmB;gBAAgB;gBAAe;aAAoB;YAC/E,WAAW;gBAAC;gBAAW;aAAa;YACpC,WAAW;gBACT,UAAU;gBACV,WAAW;gBACX,cAAc;YAChB;QACF;QACA,gBAAgB;QAChB,SAAS;YACP,iBAAiB;gBACf,OAAO;gBACP,MAAM;gBACN,OAAO;gBACP,YAAY;YACd;YACA,eAAe;gBACb,QAAQ;gBACR,SAAS;gBACT,WAAW;YACb;QACF;QACA,eAAe;QACf,SAAS;YACP,YAAY;YACZ,WAAW;YACX,QAAQ;YACR,SAAS;QACX;IACF;IAEA,mCAAmC;IACnC,MAAM,kBAAkB,CAAC;QACvB,MAAM,SAAS,SAAS,WAAW;QAEnC,mBAAmB;QACnB,MAAM,gBAAgB;YACpB,MAAM;gBAAC;gBAAM;gBAAQ;aAAO;YAC5B,MAAM;gBAAC;gBAAO;gBAAQ;gBAAS;aAAO;YACtC,OAAO;gBAAC;gBAAO;gBAAS;aAAO;YAC/B,KAAK;gBAAC;gBAAO;gBAAO;aAAQ;YAC5B,KAAK;gBAAC;gBAAS;gBAAO;aAAM;YAC5B,KAAK;gBAAC;gBAAM;gBAAO;aAAM;QAC3B;QAEA,IAAI,eAAe;QACnB,KAAK,MAAM,CAAC,MAAM,SAAS,IAAI,OAAO,OAAO,CAAC,eAAgB;YAC5D,IAAI,SAAS,IAAI,CAAC,CAAA,UAAW,OAAO,QAAQ,CAAC,WAAW;gBACtD,eAAe;gBACf;YACF;QACF;QAEA,OAAO;YAAE;YAAc,kBAAkB;YAAU,mBAAmB;QAAO;IAC/E;IAEA,MAAM,8BAA8B,CAAC;QACnC,MAAM,EAAE,YAAY,EAAE,iBAAiB,EAAE,GAAG;QAE5C,yBAAyB;QACzB,IAAI,kBAAkB,QAAQ,CAAC,YAAY,kBAAkB,QAAQ,CAAC,eAAe;YACnF,IAAI,iBAAiB,QAAQ;gBAC3B,OAAO,CAAC,oBAAoB,EAAE,cAAc,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,cAAc,UAAU,CAAC,QAAQ,CAAC,6BAA6B,CAAC;YACpI;YACA,IAAI,iBAAiB,SAAS;gBAC5B,OAAO,CAAC,oBAAoB,EAAE,cAAc,UAAU,CAAC,QAAQ,CAAC,sDAAsD,CAAC;YACzH;YACA,IAAI,iBAAiB,QAAQ;gBAC3B,OAAO,CAAC,GAAG,EAAE,cAAc,UAAU,CAAC,IAAI,CAAC,uFAAuF,CAAC;YACrI;QACF;QAEA,kBAAkB;QAClB,IAAI,kBAAkB,QAAQ,CAAC,WAAW,kBAAkB,QAAQ,CAAC,WAAW;YAC9E,IAAI,kBAAkB,QAAQ,CAAC,YAAY,kBAAkB,QAAQ,CAAC,UAAU;gBAC9E,OAAO,CAAC,GAAG,EAAE,cAAc,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,yEAAyE,CAAC;YACrI;YACA,IAAI,kBAAkB,QAAQ,CAAC,WAAW,kBAAkB,QAAQ,CAAC,SAAS;gBAC5E,OAAO,CAAC,GAAG,EAAE,cAAc,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,wCAAwC,CAAC;YACnG;YACA,OAAO,CAAC,4HAA4H,CAAC;QACvI;QAEA,cAAc;QACd,IAAI,kBAAkB,QAAQ,CAAC,WAAW,kBAAkB,QAAQ,CAAC,YAAY;YAC/E,IAAI,iBAAiB,OAAO;gBAC1B,OAAO,CAAC,GAAG,EAAE,cAAc,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,iDAAiD,CAAC;YAC5G;YACA,OAAO,CAAC,2CAA2C,EAAE,cAAc,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,iBAAiB,EAAE,cAAc,OAAO,CAAC,aAAa,CAAC,SAAS,EAAE;QACrK;QAEA,gBAAgB;QAChB,IAAI,kBAAkB,QAAQ,CAAC,YAAY,kBAAkB,QAAQ,CAAC,WAAW;YAC/E,OAAO,CAAC,8GAA8G,CAAC;QACzH;QAEA,wBAAwB;QACxB,IAAI,kBAAkB,QAAQ,CAAC,UAAU,kBAAkB,QAAQ,CAAC,WAAW,kBAAkB,QAAQ,CAAC,UAAU;YAClH,OAAO,CAAC;4BACc,EAAE,cAAc,UAAU,CAAC,SAAS,CAAC,QAAQ,CAAC;uBACnD,EAAE,cAAc,UAAU,CAAC,SAAS,CAAC,YAAY,CAAC;2BAC9C,EAAE,cAAc,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC;2BAC/C,CAAC;QACxB;QAEA,kBAAkB;QAClB,IAAI,kBAAkB,QAAQ,CAAC,gBAAgB,kBAAkB,QAAQ,CAAC,eAAe;YACvF,OAAO,CAAC,GAAG,EAAE,cAAc,OAAO,CAAC,UAAU,CAAC,qDAAqD,CAAC;QACtG;QAEA,IAAI,kBAAkB,QAAQ,CAAC,YAAY,kBAAkB,QAAQ,CAAC,cAAc;YAClF,OAAO,CAAC,GAAG,EAAE,cAAc,OAAO,CAAC,SAAS,CAAC,yDAAyD,CAAC;QACzG;QAEA,oBAAoB;QACpB,IAAI,kBAAkB,QAAQ,CAAC,YAAY,kBAAkB,QAAQ,CAAC,YAAY,kBAAkB,QAAQ,CAAC,OAAO;YAClH,MAAM,YAAY;gBAChB;gBACA;gBACA;aACD;YACD,OAAO,SAAS,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,UAAU,MAAM,EAAE;QAChE;QAEA,IAAI,kBAAkB,QAAQ,CAAC,WAAW,kBAAkB,QAAQ,CAAC,WAAW;YAC9E,OAAO;QACT;QAEA,8BAA8B;QAC9B,OAAO,CAAC;;;;;;;;2CAQ+B,CAAC;IAC1C;IAEA,MAAM,aAAa,CAAC;QAClB,sCAAsC;QACtC,uBAAuB,CAAA,OAAQ;mBAAI;gBAAM;aAAS;QAElD,eAAe;QACf,MAAM,WAAW,gBAAgB;QAEjC,eAAe;QACf,MAAM,WAAW,4BAA4B;QAE7C,sBAAsB;QACtB,gBAAgB,CAAA,OAAQ,CAAC;gBACvB,GAAG,IAAI;gBACP,CAAC,SAAS,EAAE;gBACZ,gBAAgB,CAAC,KAAK,cAAc,IAAI,CAAC,IAAI;YAC/C,CAAC;QAED,OAAO;IACT;IAEA,2BAA2B;IAC3B,MAAM,mBAAmB;QACvB,MAAM,cAAc;YAClB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,OAAO,WAAW,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,YAAY,MAAM,EAAE;IACpE;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,UAAU,IAAI,IAAI;QAEvB,MAAM,cAAc;YAAE,MAAM;YAAQ,MAAM;QAAU;QACpD,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAE1C,qBAAqB;QACrB,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP,gBAAgB,CAAC,KAAK,cAAc,IAAI,CAAC,IAAI;gBAC7C,cAAc;gBACd,WAAW,IAAI,OAAO,WAAW;YACnC,CAAC;QAED,aAAa;QACb,YAAY;QAEZ,kCAAkC;QAClC,WAAW;YACT,MAAM,cAAc,WAAW;YAC/B,MAAM,aAAa;YAEnB,MAAM,mBAAmB,GAAG,YAAY,eAAe,EAAE,YAAY;YAErE,MAAM,aAAa;gBAAE,MAAM;gBAAO,MAAM;YAAiB;YACzD,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAW;YACzC,YAAY;QACd,GAAG,KAAK,MAAM,KAAK,OAAO,MAAM,+BAA+B;IACjE;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB;QACF;IACF;IAEA,MAAM,aAAa;QACjB,YAAY,CAAC;IACf;IAEA,qBACE;;0BAEE,6LAAC;gBACC,OAAO;oBACL,UAAU;oBACV,QAAQ;oBACR,OAAO;oBACP,QAAQ;oBACR,SAAS;oBACT,YAAY;oBACZ,OAAO;oBACP,QAAQ;oBACR,cAAc;oBACd,QAAQ;oBACR,WAAW;oBACX,UAAU;oBACV,YAAY;oBACZ,YAAY;oBACZ,WAAW,WAAW,eAAe;gBACvC;gBACA,SAAS;gBACT,cAAc,CAAC;oBACb,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;oBAC3B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;gBAC7B;gBACA,cAAc,CAAC;oBACb,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG,WAAW,eAAe;oBACrD,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;gBAC7B;0BAEC,WAAW,MAAM;;;;;;YAInB,0BACC,6LAAC;gBACC,OAAO;oBACL,UAAU;oBACV,QAAQ;oBACR,OAAO;oBACP,OAAO;oBACP,QAAQ;oBACR,iBAAiB;oBACjB,WAAW;oBACX,cAAc;oBACd,QAAQ;oBACR,SAAS;oBACT,eAAe;gBACjB;;kCAGA,6LAAC;wBACC,OAAO;4BACL,iBAAiB;4BACjB,OAAO;4BACP,SAAS;4BACT,qBAAqB;4BACrB,sBAAsB;wBACxB;kCAEA,cAAA,6LAAC;4BAAG,OAAO;gCAAE,QAAQ;gCAAG,SAAS;gCAAQ,YAAY;gCAAU,KAAK;4BAAM;;gCAAG;8CAE3E,6LAAC;oCAAK,OAAO;wCAAE,UAAU;wCAAQ,SAAS;oCAAI;8CAAG;;;;;;;;;;;;;;;;;kCAKrD,6LAAC;wBAAI,OAAO;4BAAE,MAAM;4BAAG,SAAS;4BAAQ,WAAW;4BAAQ,SAAS;4BAAQ,eAAe;wBAAS;;0CAElG,6LAAC;gCAAI,OAAO;oCAAE,MAAM;oCAAG,WAAW;oCAAQ,cAAc;gCAAO;;oCAC5D,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;4CAEC,OAAO;gDACL,cAAc;gDACd,SAAS;gDACT,gBAAgB,QAAQ,IAAI,KAAK,SAAS,aAAa;4CACzD;sDAEA,cAAA,6LAAC;gDACC,OAAO;oDACL,UAAU;oDACV,SAAS;oDACT,cAAc;oDACd,iBAAiB,QAAQ,IAAI,KAAK,SAC9B,6CACA;oDACJ,OAAO,QAAQ,IAAI,KAAK,SAAS,UAAU;oDAC3C,UAAU;oDACV,YAAY;oDACZ,WAAW;oDACX,QAAQ,QAAQ,IAAI,KAAK,QAAQ,sBAAsB;oDACvD,YAAY;gDACd;0DAEC,QAAQ,IAAI;;;;;;2CAvBV;;;;;oCA2BR,0BACC,6LAAC;wCAAI,OAAO;4CAAE,SAAS;4CAAQ,gBAAgB;4CAAc,cAAc;wCAAO;kDAChF,cAAA,6LAAC;4CACC,OAAO;gDACL,SAAS;gDACT,cAAc;gDACd,iBAAiB;gDACjB,UAAU;gDACV,QAAQ;gDACR,WAAW;gDACX,WAAW;4CACb;sDACD;;;;;;;;;;;kDAKL,6LAAC;wCAAI,KAAK;;;;;;;;;;;;0CAIZ,6LAAC;gCAAI,OAAO;oCAAE,SAAS;oCAAQ,KAAK;gCAAM;;kDACxC,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;wCAC5C,WAAW;wCACX,aAAY;wCACZ,OAAO;4CACL,MAAM;4CACN,SAAS;4CACT,QAAQ;4CACR,cAAc;4CACd,SAAS;4CACT,UAAU;wCACZ;;;;;;kDAEF,6LAAC;wCACC,SAAS;wCACT,OAAO;4CACL,SAAS;4CACT,iBAAiB;4CACjB,OAAO;4CACP,QAAQ;4CACR,cAAc;4CACd,QAAQ;4CACR,UAAU;wCACZ;kDACD;;;;;;;;;;;;;;;;;;kCAOL,6LAAC;wBACC,OAAO;4BACL,UAAU;4BACV,KAAK;4BACL,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,UAAU;4BACV,QAAQ;wBACV;wBACA,SAAS;kCACV;;;;;;;;;;;;;;AAOX;GApaM;KAAA;uCAsaS", "debugId": null}}]}