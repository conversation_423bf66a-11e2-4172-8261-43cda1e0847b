{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_35b02a57.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_35b02a57-module__BOCH1a__className\",\n  \"variable\": \"geist_35b02a57-module__BOCH1a__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_35b02a57.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist%22,%22arguments%22:[{%22variable%22:%22--font-geist-sans%22,%22subsets%22:[%22latin%22],%22display%22:%22swap%22}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_87bc4ef9.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_mono_87bc4ef9-module__963okG__className\",\n  \"variable\": \"geist_mono_87bc4ef9-module__963okG__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_87bc4ef9.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22variable%22:%22--font-geist-mono%22,%22subsets%22:[%22latin%22],%22display%22:%22swap%22}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,0JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,0JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,0JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/components/Navbar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Navbar.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Navbar.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/components/Navbar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Navbar.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Navbar.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/components/Footer.tsx"], "sourcesContent": ["// components/Footer.js (أو .tsx إذا كنت تستخدم TypeScript)\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport { FaPhone, FaMapMarkerAlt, FaEnvelope } from 'react-icons/fa';\r\n\r\nconst Footer = () => {\r\n  return (\r\n    <footer className=\"bg-white text-gray-800 py-10 px-6 md:px-20 flex flex-col md:flex-row justify-between gap-10 border-t border-gray-200\">\r\n      {/* Left section */}\r\n      <div className=\"md:w-1/2\">\r\n        <div className=\"flex items-center mb-4\">\r\n          <img src=\"/logo.png\" alt=\"IREGO Logo\" className=\"h-20 w-auto mr-3\" />\r\n        </div>\r\n        <p className=\"text-sm text-gray-600 mb-4\">\r\n          Official website for the 2025 Tripoli conference on renewable energy, oil & gas, and climate change—\r\n          featuring event details, paper submissions, journals, and collaboration opportunities for global experts.\r\n        </p>\r\n\r\n        <div className=\"text-sm space-y-2 text-orange-600\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <FaMapMarkerAlt /> \r\n            <span>Omar Al-Mukhtar Street, Tripoli - Libya</span>\r\n          </div>\r\n          <div className=\"flex items-center gap-2\">\r\n            <FaPhone />\r\n            <span>021884-444-4882</span>\r\n          </div>\r\n          <div className=\"flex items-center gap-2\">\r\n            <FaPhone />\r\n            <span>021884-444-4883</span>\r\n          </div>\r\n          <div className=\"flex items-center gap-2\">\r\n            <FaPhone />\r\n            <span>0021893-259-9265</span>\r\n          </div>\r\n          <div className=\"flex items-center gap-2\">\r\n            <FaEnvelope />\r\n            <span><EMAIL></span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Right section */}\r\n      <div className=\"md:w-1/2 grid grid-cols-2 gap-4 text-sm text-gray-700\">\r\n        <div className=\"space-y-2\">\r\n          <li><Link className=\"hover:text-orange-400 transition\" href=\"/\">Home</Link></li>\r\n\r\n          <Link href=\"/about\"><p className=\"cursor-pointer hover:text-orange-500\">About us</p></Link>\r\n          <Link href=\"/committees\"><p className=\"cursor-pointer hover:text-orange-500\">Committees\r\n</p></Link>\r\n          <Link href=\"/call-for-papers\"><p className=\"cursor-pointer hover:text-orange-500\">Call For Papers</p></Link>\r\n          <Link href=\"/submission\"><p className=\"cursor-pointer hover:text-orange-500\">Submission</p></Link>\r\n        </div>\r\n        <div className=\"space-y-2\">\r\n          <h4 className=\"font-semibold text-black\">Registration</h4>\r\n          <Link href=\"/venue\"><p className=\"cursor-pointer hover:text-orange-500\">Venue</p></Link>\r\n          <Link href=\"/contact\"><p className=\"cursor-pointer hover:text-orange-500\">Contact us</p></Link>\r\n          <Link href=\"/expo\"><p className=\"cursor-pointer hover:text-orange-500\">IREGO Expo</p></Link>\r\n          <Link href=\"/the-best\"><p className=\"cursor-pointer hover:text-orange-500\">IREGO The Best</p></Link>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n};\r\n\r\nexport default Footer;\r\n"], "names": [], "mappings": "AAAA,2DAA2D;;;;;AAE3D;AACA;;;;AAEA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,KAAI;4BAAY,KAAI;4BAAa,WAAU;;;;;;;;;;;kCAElD,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAK1C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8IAAA,CAAA,iBAAc;;;;;kDACf,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8IAAA,CAAA,UAAO;;;;;kDACR,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8IAAA,CAAA,UAAO;;;;;kDACR,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8IAAA,CAAA,UAAO;;;;;kDACR,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8IAAA,CAAA,aAAU;;;;;kDACX,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,WAAU;oCAAmC,MAAK;8CAAI;;;;;;;;;;;0CAEhE,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CAAS,cAAA,8OAAC;oCAAE,WAAU;8CAAuC;;;;;;;;;;;0CACxE,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CAAc,cAAA,8OAAC;oCAAE,WAAU;8CAAuC;;;;;;;;;;;0CAE7E,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CAAmB,cAAA,8OAAC;oCAAE,WAAU;8CAAuC;;;;;;;;;;;0CAClF,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CAAc,cAAA,8OAAC;oCAAE,WAAU;8CAAuC;;;;;;;;;;;;;;;;;kCAE/E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2B;;;;;;0CACzC,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CAAS,cAAA,8OAAC;oCAAE,WAAU;8CAAuC;;;;;;;;;;;0CACxE,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CAAW,cAAA,8OAAC;oCAAE,WAAU;8CAAuC;;;;;;;;;;;0CAC1E,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CAAQ,cAAA,8OAAC;oCAAE,WAAU;8CAAuC;;;;;;;;;;;0CACvE,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CAAY,cAAA,8OAAC;oCAAE,WAAU;8CAAuC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKrF;uCAEe", "debugId": null}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/components/ErrorBoundary.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ErrorBoundary.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ErrorBoundary.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 466, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/components/ErrorBoundary.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ErrorBoundary.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ErrorBoundary.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 480, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 490, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/context/UserContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const UserProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call UserProvider() from the server but UserProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/context/UserContext.tsx <module evaluation>\",\n    \"UserProvider\",\n);\nexport const useUser = registerClientReference(\n    function() { throw new Error(\"Attempted to call useUser() from the server but useUser is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/context/UserContext.tsx <module evaluation>\",\n    \"useUser\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,6DACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,6DACA", "debugId": null}}, {"offset": {"line": 508, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/context/UserContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const UserProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call UserProvider() from the server but UserProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/context/UserContext.tsx\",\n    \"UserProvider\",\n);\nexport const useUser = registerClientReference(\n    function() { throw new Error(\"Attempted to call useUser() from the server but useUser is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/context/UserContext.tsx\",\n    \"useUser\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,yCACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,yCACA", "debugId": null}}, {"offset": {"line": 526, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 536, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/components/ChatbotPopup.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ChatbotPopup.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ChatbotPopup.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 550, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/components/ChatbotPopup.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ChatbotPopup.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ChatbotPopup.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,4CACA", "debugId": null}}, {"offset": {"line": 564, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 574, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/app/layout.tsx"], "sourcesContent": ["import type { Metada<PERSON> } from \"next\";\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from \"next/font/google\";\nimport \"./globals.css\";\nimport Navbar from \"../components/Navbar\";\nimport Footer from \"../components/Footer\";\nimport ErrorBoundary from \"../components/ErrorBoundary\";\nimport { UserProvider } from '../context/UserContext';\nimport ChatbotPopup from '../components/ChatbotPopup'\n\nconst geistSans = Geist({\n  variable: \"--font-geist-sans\",\n  subsets: [\"latin\"],\n  display: \"swap\",\n});\n\nconst geistMono = Geist_Mono({\n  variable: \"--font-geist-mono\",\n  subsets: [\"latin\"],\n  display: \"swap\",\n});\n\nexport const metadata: Metadata = {\n  title: \"International Conference 2025\",\n  description: \"Renewable Energy, Gas & Oil, and Climate Change Conference\",\n  viewport: \"width=device-width, initial-scale=1\",\n  robots: \"index, follow\",\n  authors: [{ name: \"IREGO Conference Team\" }],\n  keywords: [\"renewable energy\", \"gas\", \"oil\", \"climate change\", \"conference\", \"Libya\", \"Tripoli\"],\n};\n\nexport default function RootLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  return (\n    <html lang=\"en\" suppressHydrationWarning>\n      <head>\n        <meta name=\"theme-color\" content=\"#f97316\" />\n        <meta name=\"format-detection\" content=\"telephone=no\" />\n      </head>\n      <body\n        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen flex flex-col`}\n        suppressHydrationWarning\n      >\n        {/* ✅ الشريط البرتقالي المتحرك */}\n        <div className=\"bg-orange-600 overflow-hidden whitespace-nowrap\">\n          <div className=\"animate-marquee text-white text-sm py-2 px-4 inline-block\">\n            International Renewable Energy, Gas & Oil, and Climate Change Conference — November 25–27, 2025 — Tripoli, Libya\n          </div>\n        </div>\n\n        <ErrorBoundary>\n          <UserProvider>\n            <Navbar />\n\n            <main className=\"flex-grow\">{children}</main>\n      <ChatbotPopup />\n            <Footer />\n          </UserProvider>\n        </ErrorBoundary>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAGA;AACA;AACA;AACA;AACA;;;;;;;;;;AAcO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;IACV,QAAQ;IACR,SAAS;QAAC;YAAE,MAAM;QAAwB;KAAE;IAC5C,UAAU;QAAC;QAAoB;QAAO;QAAO;QAAkB;QAAc;QAAS;KAAU;AAClG;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGT;IACC,qBACE,8OAAC;QAAK,MAAK;QAAK,wBAAwB;;0BACtC,8OAAC;;kCACC,8OAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;kCACjC,8OAAC;wBAAK,MAAK;wBAAmB,SAAQ;;;;;;;;;;;;0BAExC,8OAAC;gBACC,WAAW,GAAG,yIAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,8IAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,uCAAuC,CAAC;gBAC/F,wBAAwB;;kCAGxB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCAA4D;;;;;;;;;;;kCAK7E,8OAAC,mIAAA,CAAA,UAAa;kCACZ,cAAA,8OAAC,8HAAA,CAAA,eAAY;;8CACX,8OAAC,4HAAA,CAAA,UAAM;;;;;8CAEP,8OAAC;oCAAK,WAAU;8CAAa;;;;;;8CACnC,8OAAC,iIAAA,CAAA,UAAY;;;;;8CACP,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMnB", "debugId": null}}]}