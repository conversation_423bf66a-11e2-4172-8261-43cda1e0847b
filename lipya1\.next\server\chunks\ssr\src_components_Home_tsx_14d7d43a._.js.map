{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/components/Home.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Image from \"next/image\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { Swiper, SwiperSlide } from \"swiper/react\";\r\nimport \"swiper/css\";\r\n\r\n// Safe Image Component with fallback\r\ninterface SafeImageProps {\r\n  src: string;\r\n  alt: string;\r\n  width?: number;\r\n  height?: number;\r\n  className?: string;\r\n  fill?: boolean;\r\n  priority?: boolean;\r\n  suppressHydrationWarning?: boolean;\r\n}\r\n\r\nconst SafeImage = ({\r\n  src,\r\n  alt,\r\n  width,\r\n  height,\r\n  className,\r\n  fill,\r\n  priority,\r\n  suppressHydrationWarning,\r\n}: SafeImageProps) => {\r\n  const [imgSrc, setImgSrc] = useState(src);\r\n  const [hasError, setHasError] = useState(false);\r\n\r\n  const handleError = () => {\r\n    if (!hasError) {\r\n      setHasError(true);\r\n      setImgSrc(\"/logo.png\"); // fallback to logo\r\n    }\r\n  };\r\n\r\n  if (fill) {\r\n    return (\r\n      <Image\r\n        src={imgSrc}\r\n        alt={alt || \"\"}\r\n        fill\r\n        className={className}\r\n        onError={handleError}\r\n        suppressHydrationWarning={suppressHydrationWarning}\r\n        priority={priority}\r\n      />\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Image\r\n      src={imgSrc}\r\n      alt={alt || \"\"}\r\n      width={width || 100}\r\n      height={height || 100}\r\n      className={className}\r\n      onError={handleError}\r\n      suppressHydrationWarning={suppressHydrationWarning}\r\n      priority={priority}\r\n    />\r\n  );\r\n};\r\n\r\nconst sliderImages = [\r\n  \"/photo1.png\",\r\n  \"/photoo1.jpg\",\r\n  \"/photoo2.jpg\",\r\n  \"/photoo3.jpg\",\r\n  \"/photoo4.jpg\",\r\n  \"/photoo5.jpg\",\r\n  \"/photoo6.jpg\",\r\n];\r\n\r\nconst sponsors = [\r\n  \"/photo5.png\",\r\n  \"/photo6.png\",\r\n  \"/photo7.png\",\r\n  \"/photo8.png\",\r\n  \"/ckt1.png\",\r\n  \"/ckt2.png\",\r\n  \"/ckt3.png\",\r\n];\r\n\r\nexport default function HomePage() {\r\n  const [mounted, setMounted] = useState(false);\r\n  const [showInfo, setShowInfo] = useState(false);\r\n  const [currentSlide, setCurrentSlide] = useState(0);\r\n  const [time, setTime] = useState({\r\n    seconds: 0,\r\n    minutes: 0,\r\n    hours: 0,\r\n    days: 0,\r\n    weeks: 0,\r\n    months: 0,\r\n  });\r\n\r\n  useEffect(() => {\r\n    setMounted(true);\r\n\r\n    const STORAGE_KEY = \"countdownStart\";\r\n    let startTime: string | null = null;\r\n\r\n    try {\r\n      startTime = localStorage.getItem(STORAGE_KEY);\r\n      if (!startTime) {\r\n        startTime = Date.now().toString();\r\n        localStorage.setItem(STORAGE_KEY, startTime);\r\n      }\r\n    } catch {\r\n      // Fallback if localStorage is not available\r\n      startTime = Date.now().toString();\r\n    }\r\n\r\n    const updateTime = () => {\r\n      const now = Date.now();\r\n      const diffInSeconds = Math.floor((now - parseInt(startTime!)) / 1000);\r\n\r\n      const seconds = diffInSeconds % 60;\r\n      const totalMinutes = Math.floor(diffInSeconds / 60);\r\n      const minutes = totalMinutes % 60;\r\n      const totalHours = Math.floor(totalMinutes / 60);\r\n      const hours = totalHours % 24;\r\n      const totalDays = Math.floor(totalHours / 24);\r\n      const weeks = Math.floor(totalDays / 7);\r\n      const days = totalDays % 7;\r\n      const months = Math.floor(totalDays / 30);\r\n\r\n      setTime({ seconds, minutes, hours, days, weeks, months });\r\n    };\r\n\r\n    updateTime();\r\n    const timer = setInterval(updateTime, 1000);\r\n\r\n    // Timer للسلايدر\r\n    const slideTimer = setInterval(() => {\r\n      setCurrentSlide(prev => (prev + 1) % sliderImages.length);\r\n    }, 5000);\r\n\r\n    return () => {\r\n      clearInterval(timer);\r\n      clearInterval(slideTimer);\r\n    };\r\n  }, []);\r\n\r\n  if (!mounted) return null;\r\n\r\n  return (\r\n    <div className=\"relative w-full text-black overflow-hidden min-h-screen\">\r\n      {/* 👉 سلايدر بديل بدون Swiper */}\r\n      <div className=\"relative w-full h-[500px] sm:h-[550px] md:h-[600px]\">\r\n        {/* الصورة الحالية */}\r\n        <div className=\"absolute inset-0 z-0\">\r\n          <Image\r\n            src={sliderImages[currentSlide]}\r\n            alt={`Slide ${currentSlide + 1}`}\r\n            fill\r\n            className=\"object-cover transition-opacity duration-1000\"\r\n            priority\r\n            suppressHydrationWarning\r\n            onError={() => console.log(`Failed to load: ${sliderImages[currentSlide]}`)}\r\n            onLoad={() => console.log(`Loaded: ${sliderImages[currentSlide]}`)}\r\n          />\r\n          {/* مؤشر رقم الصورة */}\r\n          <div className=\"absolute top-4 left-4 bg-red-500 text-white px-4 py-2 rounded-full z-30 text-xl font-bold\">\r\n            {currentSlide + 1} / {sliderImages.length}\r\n          </div>\r\n        </div>\r\n\r\n        {/* طبقة التعتيم */}\r\n        <div className=\"absolute inset-0 bg-black/40 z-10\" />\r\n\r\n        {/* النص فوق السلايدر */}\r\n        <div className=\"absolute inset-0 z-20 flex flex-col items-center justify-center text-center px-4 max-w-4xl mx-auto\">\r\n          <h1 className=\"text-3xl md:text-5xl font-bold leading-snug text-white\">\r\n            Pioneering Sustainable{\" \"}\r\n            <span className=\"bg-orange-500 px-2 py-1 rounded text-white inline-block\">\r\n              Energy\r\n            </span>{\" \"}\r\n            for a Greener Future\r\n          </h1>\r\n          <p className=\"mt-6 text-lg md:text-xl max-w-xl text-white\">\r\n            Join global experts, researchers, and policymakers in Tripoli to\r\n            explore innovations in renewable energy, oil & gas, and climate\r\n            solutions.\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 👉 العداد */}\r\n      <div className=\"mt-6 text-center\">\r\n        <span className=\"text-black text-xl font-bold\">Conference </span>\r\n        <span className=\"text-orange-500 text-xl font-semibold\">Countdown</span>\r\n      </div>\r\n      <div className=\"mt-6 flex justify-center gap-6 px-4 flex-wrap max-w-4xl mx-auto\">\r\n        {[\r\n          { label: \"SECOND\", value: time.seconds },\r\n          { label: \"MINUTE\", value: time.minutes },\r\n          { label: \"HOURS\", value: time.hours },\r\n          { label: \"DAYS\", value: time.days },\r\n          { label: \"WEEKS\", value: time.weeks },\r\n          { label: \"MONTH\", value: time.months },\r\n        ].map(({ label, value }) => (\r\n          <div\r\n            key={label}\r\n            className=\"bg-orange-500 text-white w-20 h-24 rounded-md flex flex-col items-center justify-center shadow-lg\"\r\n            suppressHydrationWarning\r\n          >\r\n            <span className=\"text-3xl font-bold\" suppressHydrationWarning>{value}</span>\r\n            <span className=\"text-sm mt-1\">{label}</span>\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      {/* 👉 معلومات المؤتمر + READ MORE */}\r\n      <div className=\"mt-10 text-center px-4 max-w-3xl mx-auto\">\r\n        <p className=\"text-lg md:text-xl font-semibold\">\r\n          Get to know about{\" \"}\r\n          <span className=\"text-orange-500 uppercase font-bold\">CONFERENCE</span>\r\n        </p>\r\n        <button\r\n          onClick={() => setShowInfo(!showInfo)}\r\n          className=\" mt-4 bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-md font-semibold transition\"\r\n          suppressHydrationWarning\r\n        >\r\n          READ MORE\r\n        </button>\r\n\r\n        {showInfo && (\r\n          <div className=\"mt-6 text-left bg-gray-100 p-6 rounded-md shadow-md text-gray-900 text-base md:text-lg\">\r\n            The Renewable Energy, Gas & Oil, and Climate Change Conference will\r\n            take place in Tripoli, Libya, in November 2025, bringing together\r\n            experts, researchers, industry leaders, and policymakers to address\r\n            pressing energy and environmental challenges. This pioneering event\r\n            aims to bridge the gap between academia, industry, and government\r\n            by fostering dialogue and collaboration on innovative solutions in\r\n            renewable energy, oil and gas, and climate change mitigation.\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n            {/* فيديو تعريفي بالمؤتمر */}\r\n      <div className=\"mt-16 px-4 max-w-4xl mx-auto\">\r\n        <video\r\n          src=\"/Tripoli3.mp4\"\r\n          controls\r\n          autoPlay\r\n          muted\r\n          loop\r\n          className=\"w-full rounded-lg shadow-lg\"\r\n          suppressHydrationWarning\r\n        >\r\n          Your browser does not support the video tag.\r\n        </video>\r\n      </div>\r\n\r\n      {/* Scientific Journals Section */}\r\n      <div className=\"mt-16 text-center px-4\">\r\n        <h2 className=\"text-2xl md:text-3xl font-bold mb-8\">\r\n          <span className=\"text-orange-500\">Scientific</span> Journals\r\n        </h2>\r\n\r\n        <Swiper\r\n          spaceBetween={20}\r\n          slidesPerView={1.2}\r\n          breakpoints={{\r\n            640: { slidesPerView: 2 },\r\n            1024: { slidesPerView: 3 },\r\n          }}\r\n          suppressHydrationWarning\r\n        >\r\n    {/* المجلة 1 */}\r\n    <SwiperSlide>\r\n      <div className=\"flex flex-col items-center\">\r\n        <SafeImage\r\n          src=\"/photo2.png\"\r\n          alt=\"Libya Journal\"\r\n          width={130}\r\n          height={120}\r\n          className=\"rounded shadow-md\"\r\n          suppressHydrationWarning\r\n        />\r\n        <p className=\"mt-4 text-center font-medium\">\r\n          Libya Journal for Applied Sciences and Technology\r\n        </p>\r\n      </div>\r\n    </SwiperSlide>\r\n\r\n    {/* المجلة 2 */}\r\n    <SwiperSlide>\r\n      <div className=\"flex flex-col items-center\">\r\n        <SafeImage\r\n          src=\"/photo3.png\"\r\n          alt=\"Solar Journal\"\r\n          width={120}\r\n          height={120}\r\n          className=\"rounded shadow-md\"\r\n          suppressHydrationWarning\r\n        />\r\n        <p className=\"mt-4 text-center font-medium\">\r\n          Solar Energy and Sustainable Development Journal\r\n        </p>\r\n      </div>\r\n    </SwiperSlide>\r\n\r\n    {/* المجلة 3 */}\r\n    <SwiperSlide>\r\n      <div className=\"flex flex-col items-center\">\r\n        <SafeImage\r\n          src=\"/photo4.png\"\r\n          alt=\"Pure Sciences Journal\"\r\n          width={120}\r\n          height={120}\r\n          className=\"rounded shadow-md\"\r\n          suppressHydrationWarning\r\n        />\r\n        <p className=\"mt-4 text-center font-medium\">\r\n          Pure and Applied Sciences Journal\r\n        </p>\r\n      </div>\r\n    </SwiperSlide>\r\n      <SwiperSlide>\r\n      <div className=\"flex flex-col items-center\">\r\n        <SafeImage\r\n          src=\"/ff1.jpg\"\r\n          alt=\"Pure Sciences Journal\"\r\n          width={140}\r\n          height={160}\r\n          className=\"rounded shadow-md\"\r\n          suppressHydrationWarning\r\n        />\r\n        <p className=\"mt-4 text-center font-medium\">\r\n          Pure and Applied Sciences Journal\r\n        </p>\r\n      </div>\r\n        </SwiperSlide>\r\n        </Swiper>\r\n      </div>\r\n\r\n            {/* Submit Your Research Section */}\r\n      <div className=\"mt-20 px-4 w-full flex flex-col items-center text-center\">\r\n        <h2 className=\"text-lg md:text-2xl font-bold mb-4\">\r\n          Call of <span className=\"text-orange-500\">Paper</span>\r\n        </h2>\r\n\r\n        <p className=\"text-gray-700 text-sm md:text-base mb-8 max-w-2xl\">\r\n          Share your innovative research and contribute to the advancement of sustainable energy solutions.\r\n        </p>\r\n\r\n        {/* اجعل الحاوية أفقيّة */}\r\n        <div className=\"flex flex-row flex-wrap justify-center gap-8 max-w-xl mx-auto text-left\">\r\n          {/* العمود الأيسر */}\r\n          <div className=\"flex flex-col gap-4 min-w-[140px]\">\r\n            <div className=\"flex items-center gap-2 text-xs sm:text-sm\">\r\n              <i className=\"fas fa-solar-panel text-orange-500\"></i>\r\n              <span className=\"font-medium\">Renewable Energy</span>\r\n            </div>\r\n            <div className=\"flex items-center gap-2 text-xs sm:text-sm\">\r\n              <i className=\"fas fa-globe-americas text-orange-500\"></i>\r\n              <span className=\"font-medium\">Climate Change</span>\r\n            </div>\r\n          </div>\r\n\r\n          {/* العمود الأيمن */}\r\n          <div className=\"flex flex-col gap-4 min-w-[140px]\">\r\n            <div className=\"flex items-center gap-2 text-xs sm:text-sm\">\r\n              <i className=\"fas fa-industry text-orange-500\"></i>\r\n              <span className=\"font-medium\">Oil &amp; Gas Transition</span>\r\n            </div>\r\n            <div className=\"flex items-center gap-2 text-xs sm:text-sm\">\r\n              <i className=\"fas fa-leaf text-orange-500\"></i>\r\n              <span className=\"font-medium\">Sustainable Development</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"mt-10 flex justify-center\">\r\n        <a\r\n          href=\"/call-for-papers\"\r\n          className=\"bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-md font-semibold transition flex items-center gap-2\"\r\n        >\r\n          <i className=\"fas fa-file-upload\"></i>\r\n          Read More\r\n        </a>\r\n      </div>\r\n\r\n  <div\r\n      style={{\r\n        display: 'flex',\r\n        justifyContent: 'center',\r\n        marginTop: '100px',\r\n      }}\r\n    >\r\n      <div\r\n        style={{\r\n          width: '700px',\r\n          border: ' #ccc',\r\n          borderRadius: '8px',\r\n          overflow: 'hidden',\r\n          boxShadow: '12px  12px 12px rgba(0,0,0,0.1)',\r\n        }}\r\n      >\r\n        <a\r\n          href=\"https://niclibya.com/\"\r\n          target=\"_blank\"\r\n          rel=\"noopener noreferrer\"\r\n        >\r\n          <Image\r\n            src=\"/sp1.png\"\r\n            alt=\"صورة الكارد\"\r\n            width={500}\r\n            height={300}\r\n            style={{ display: 'block', width: '100%', height: 'auto' }}\r\n          />\r\n        </a>\r\n      </div>\r\n    </div>\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n      <h2 className=\"text-2xl md:text-3xl font-bold text-center mt-16\">\r\n        <span className=\"text-orange-500\">Conference</span> Timeline\r\n      </h2>\r\n      <div className=\"mt-10 flex flex-row flex-wrap items-center justify-center gap-12 px-4 max-w-5xl mx-auto\">\r\n  {/* النقطة 1 */}\r\n  <div className=\"flex flex-col items-center min-w-[150px]\">\r\n    <div className=\"bg-orange-500 text-white text-sm px-4 py-1 rounded-full shadow-md mb-2\">\r\n      June 15, 2025\r\n    </div>\r\n    <div className=\"w-4 h-4 bg-orange-500 rounded-full shadow-lg mb-2\" />\r\n    <p className=\"text-center text-sm md:text-base font-medium\">Abstract Submission Deadline</p>\r\n  </div>\r\n\r\n  {/* النقطة 2 */}\r\n  <div className=\"flex flex-col items-center min-w-[150px]\">\r\n    <div className=\"w-4 h-4 bg-orange-500 rounded-full shadow-lg mb-2\" />\r\n    <p className=\"text-center text-sm md:text-base font-medium\">Notification of Acceptance</p>\r\n    <div className=\"bg-orange-500 text-white text-sm px-4 py-1 rounded-full shadow-md mt-2\">\r\n      August 15, 2025\r\n    </div>\r\n  </div>\r\n\r\n  {/* النقطة 3 */}\r\n  <div className=\"flex flex-col items-center min-w-[150px]\">\r\n    <div className=\"bg-orange-500 text-white text-sm px-4 py-1 rounded-full shadow-md mb-2\">\r\n      October 1, 2025\r\n    </div>\r\n    <div className=\"w-4 h-4 bg-orange-500 rounded-full shadow-lg mb-2\" />\r\n    <p className=\"text-center text-sm md:text-base font-medium\">Final Paper Submission</p>\r\n  </div>\r\n\r\n  {/* النقطة 4 */}\r\n  <div className=\"flex flex-col items-center min-w-[150px]\">\r\n    <div className=\"w-4 h-4 bg-orange-500 rounded-full shadow-lg mb-2\" />\r\n    <p className=\"text-center text-sm md:text-base font-medium\">Conference Dates</p>\r\n    <div className=\"bg-orange-500 text-white text-sm px-4 py-1 rounded-full shadow-md mt-2\">\r\n      November 25–27, 2025\r\n    </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"mt-20 text-center px-4\">\r\n        <h2 className=\"text-2xl md:text-3xl font-bold\">\r\n          Our Official <span className=\"text-orange-500\">Sponsors</span>\r\n        </h2>\r\n      </div>\r\n\r\n      <div className=\"mt-10 px-4 max-w-6xl mx-auto\">\r\n        <Swiper\r\n          spaceBetween={20}\r\n          slidesPerView={2}\r\n          breakpoints={{\r\n            640: { slidesPerView: 2 },\r\n            768: { slidesPerView: 3 },\r\n            1024: { slidesPerView: 4 },\r\n          }}\r\n          suppressHydrationWarning\r\n        >\r\n        {sponsors.map((src, index) => (\r\n          <SwiperSlide key={index} className=\"flex justify-center\">\r\n            <SafeImage\r\n              src={src.startsWith('/') ? src : `/${src}`}\r\n              alt={`Sponsor ${index + 1}`}\r\n              width={150}\r\n              height={100}\r\n              className=\"rounded shadow-md max-w-[150px] h-auto object-contain\"\r\n              suppressHydrationWarning\r\n            />\r\n          </SwiperSlide>\r\n          ))}\r\n        </Swiper>\r\n      </div>\r\n\r\n      {/* كارد في وسط الشاشة */}\r\n    \r\n\r\n      {/* زر التسجيل */}\r\n    \r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;;AAmBA,MAAM,YAAY,CAAC,EACjB,GAAG,EACH,GAAG,EACH,KAAK,EACL,MAAM,EACN,SAAS,EACT,IAAI,EACJ,QAAQ,EACR,wBAAwB,EACT;IACf,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,cAAc;QAClB,IAAI,CAAC,UAAU;YACb,YAAY;YACZ,UAAU,cAAc,mBAAmB;QAC7C;IACF;IAEA,IAAI,MAAM;QACR,qBACE,8OAAC,6HAAA,CAAA,UAAK;YACJ,KAAK;YACL,KAAK,OAAO;YACZ,IAAI;YACJ,WAAW;YACX,SAAS;YACT,0BAA0B;YAC1B,UAAU;;;;;;IAGhB;IAEA,qBACE,8OAAC,6HAAA,CAAA,UAAK;QACJ,KAAK;QACL,KAAK,OAAO;QACZ,OAAO,SAAS;QAChB,QAAQ,UAAU;QAClB,WAAW;QACX,SAAS;QACT,0BAA0B;QAC1B,UAAU;;;;;;AAGhB;AAEA,MAAM,eAAe;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,WAAW;IACf;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC/B,SAAS;QACT,SAAS;QACT,OAAO;QACP,MAAM;QACN,OAAO;QACP,QAAQ;IACV;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QAEX,MAAM,cAAc;QACpB,IAAI,YAA2B;QAE/B,IAAI;YACF,YAAY,aAAa,OAAO,CAAC;YACjC,IAAI,CAAC,WAAW;gBACd,YAAY,KAAK,GAAG,GAAG,QAAQ;gBAC/B,aAAa,OAAO,CAAC,aAAa;YACpC;QACF,EAAE,OAAM;YACN,4CAA4C;YAC5C,YAAY,KAAK,GAAG,GAAG,QAAQ;QACjC;QAEA,MAAM,aAAa;YACjB,MAAM,MAAM,KAAK,GAAG;YACpB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,MAAM,SAAS,UAAW,IAAI;YAEhE,MAAM,UAAU,gBAAgB;YAChC,MAAM,eAAe,KAAK,KAAK,CAAC,gBAAgB;YAChD,MAAM,UAAU,eAAe;YAC/B,MAAM,aAAa,KAAK,KAAK,CAAC,eAAe;YAC7C,MAAM,QAAQ,aAAa;YAC3B,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa;YAC1C,MAAM,QAAQ,KAAK,KAAK,CAAC,YAAY;YACrC,MAAM,OAAO,YAAY;YACzB,MAAM,SAAS,KAAK,KAAK,CAAC,YAAY;YAEtC,QAAQ;gBAAE;gBAAS;gBAAS;gBAAO;gBAAM;gBAAO;YAAO;QACzD;QAEA;QACA,MAAM,QAAQ,YAAY,YAAY;QAEtC,iBAAiB;QACjB,MAAM,aAAa,YAAY;YAC7B,gBAAgB,CAAA,OAAQ,CAAC,OAAO,CAAC,IAAI,aAAa,MAAM;QAC1D,GAAG;QAEH,OAAO;YACL,cAAc;YACd,cAAc;QAChB;IACF,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS,OAAO;IAErB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAK,YAAY,CAAC,aAAa;gCAC/B,KAAK,CAAC,MAAM,EAAE,eAAe,GAAG;gCAChC,IAAI;gCACJ,WAAU;gCACV,QAAQ;gCACR,wBAAwB;gCACxB,SAAS,IAAM,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,YAAY,CAAC,aAAa,EAAE;gCAC1E,QAAQ,IAAM,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,YAAY,CAAC,aAAa,EAAE;;;;;;0CAGnE,8OAAC;gCAAI,WAAU;;oCACZ,eAAe;oCAAE;oCAAI,aAAa,MAAM;;;;;;;;;;;;;kCAK7C,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAyD;oCAC9C;kDACvB,8OAAC;wCAAK,WAAU;kDAA0D;;;;;;oCAElE;oCAAI;;;;;;;0CAGd,8OAAC;gCAAE,WAAU;0CAA8C;;;;;;;;;;;;;;;;;;0BAS/D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCAA+B;;;;;;kCAC/C,8OAAC;wBAAK,WAAU;kCAAwC;;;;;;;;;;;;0BAE1D,8OAAC;gBAAI,WAAU;0BACZ;oBACC;wBAAE,OAAO;wBAAU,OAAO,KAAK,OAAO;oBAAC;oBACvC;wBAAE,OAAO;wBAAU,OAAO,KAAK,OAAO;oBAAC;oBACvC;wBAAE,OAAO;wBAAS,OAAO,KAAK,KAAK;oBAAC;oBACpC;wBAAE,OAAO;wBAAQ,OAAO,KAAK,IAAI;oBAAC;oBAClC;wBAAE,OAAO;wBAAS,OAAO,KAAK,KAAK;oBAAC;oBACpC;wBAAE,OAAO;wBAAS,OAAO,KAAK,MAAM;oBAAC;iBACtC,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,iBACrB,8OAAC;wBAEC,WAAU;wBACV,wBAAwB;;0CAExB,8OAAC;gCAAK,WAAU;gCAAqB,wBAAwB;0CAAE;;;;;;0CAC/D,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;uBAL3B;;;;;;;;;;0BAWX,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;;4BAAmC;4BAC5B;0CAClB,8OAAC;gCAAK,WAAU;0CAAsC;;;;;;;;;;;;kCAExD,8OAAC;wBACC,SAAS,IAAM,YAAY,CAAC;wBAC5B,WAAU;wBACV,wBAAwB;kCACzB;;;;;;oBAIA,0BACC,8OAAC;wBAAI,WAAU;kCAAyF;;;;;;;;;;;;0BAa5G,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,KAAI;oBACJ,QAAQ;oBACR,QAAQ;oBACR,KAAK;oBACL,IAAI;oBACJ,WAAU;oBACV,wBAAwB;8BACzB;;;;;;;;;;;0BAMH,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;gCAAK,WAAU;0CAAkB;;;;;;4BAAiB;;;;;;;kCAGrD,8OAAC,0IAAA,CAAA,SAAM;wBACL,cAAc;wBACd,eAAe;wBACf,aAAa;4BACX,KAAK;gCAAE,eAAe;4BAAE;4BACxB,MAAM;gCAAE,eAAe;4BAAE;wBAC3B;wBACA,wBAAwB;;0CAG9B,8OAAC,0IAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;4CACV,wBAAwB;;;;;;sDAE1B,8OAAC;4CAAE,WAAU;sDAA+B;;;;;;;;;;;;;;;;;0CAOhD,8OAAC,0IAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;4CACV,wBAAwB;;;;;;sDAE1B,8OAAC;4CAAE,WAAU;sDAA+B;;;;;;;;;;;;;;;;;0CAOhD,8OAAC,0IAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;4CACV,wBAAwB;;;;;;sDAE1B,8OAAC;4CAAE,WAAU;sDAA+B;;;;;;;;;;;;;;;;;0CAK9C,8OAAC,0IAAA,CAAA,cAAW;0CACZ,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;4CACV,wBAAwB;;;;;;sDAE1B,8OAAC;4CAAE,WAAU;sDAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS9C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;4BAAqC;0CACzC,8OAAC;gCAAK,WAAU;0CAAkB;;;;;;;;;;;;kCAG5C,8OAAC;wBAAE,WAAU;kCAAoD;;;;;;kCAKjE,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;;;;;;0DACb,8OAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;kDAEhC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;;;;;;0DACb,8OAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;;;;;;;0CAKlC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;;;;;;0DACb,8OAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;kDAEhC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;;;;;;0DACb,8OAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMtC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,MAAK;oBACL,WAAU;;sCAEV,8OAAC;4BAAE,WAAU;;;;;;wBAAyB;;;;;;;;;;;;0BAK9C,8OAAC;gBACG,OAAO;oBACL,SAAS;oBACT,gBAAgB;oBAChB,WAAW;gBACb;0BAEA,cAAA,8OAAC;oBACC,OAAO;wBACL,OAAO;wBACP,QAAQ;wBACR,cAAc;wBACd,UAAU;wBACV,WAAW;oBACb;8BAEA,cAAA,8OAAC;wBACC,MAAK;wBACL,QAAO;wBACP,KAAI;kCAEJ,cAAA,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,OAAO;gCAAE,SAAS;gCAAS,OAAO;gCAAQ,QAAQ;4BAAO;;;;;;;;;;;;;;;;;;;;;0BAY/D,8OAAC;gBAAG,WAAU;;kCACZ,8OAAC;wBAAK,WAAU;kCAAkB;;;;;;oBAAiB;;;;;;;0BAErD,8OAAC;gBAAI,WAAU;;kCAEnB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAyE;;;;;;0CAGxF,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;;;;;;;kCAI9D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAC5D,8OAAC;gCAAI,WAAU;0CAAyE;;;;;;;;;;;;kCAM1F,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAyE;;;;;;0CAGxF,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;;;;;;;kCAI9D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAC5D,8OAAC;gCAAI,WAAU;0CAAyE;;;;;;;;;;;;;;;;;;0BAMtF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;;wBAAiC;sCAChC,8OAAC;4BAAK,WAAU;sCAAkB;;;;;;;;;;;;;;;;;0BAInD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0IAAA,CAAA,SAAM;oBACL,cAAc;oBACd,eAAe;oBACf,aAAa;wBACX,KAAK;4BAAE,eAAe;wBAAE;wBACxB,KAAK;4BAAE,eAAe;wBAAE;wBACxB,MAAM;4BAAE,eAAe;wBAAE;oBAC3B;oBACA,wBAAwB;8BAEzB,SAAS,GAAG,CAAC,CAAC,KAAK,sBAClB,8OAAC,0IAAA,CAAA,cAAW;4BAAa,WAAU;sCACjC,cAAA,8OAAC;gCACC,KAAK,IAAI,UAAU,CAAC,OAAO,MAAM,CAAC,CAAC,EAAE,KAAK;gCAC1C,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG;gCAC3B,OAAO;gCACP,QAAQ;gCACR,WAAU;gCACV,wBAAwB;;;;;;2BAPV;;;;;;;;;;;;;;;;;;;;;AAqB5B", "debugId": null}}]}