{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/app/signup/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { useUser } from \"../../context/UserContext\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport Link from \"next/link\";\r\n\r\nexport default function Signup() {\r\n  const { register } = useUser();\r\n  const router = useRouter();\r\n\r\n  const [email, setEmail] = useState(\"\");\r\n  const [password, setPassword] = useState(\"\");\r\n  const [confirmPassword, setConfirmPassword] = useState(\"\");\r\n  const [name, setName] = useState(\"\");\r\n  const [error, setError] = useState(\"\");\r\n  const [loading, setLoading] = useState(false);\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\r\n\r\n  const handleSignup = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    setError(\"\");\r\n\r\n    if (!email || !password || !confirmPassword || !name) {\r\n      setError(\"Please fill all fields\");\r\n      return;\r\n    }\r\n\r\n    if (password !== confirmPassword) {\r\n      setError(\"Passwords do not match\");\r\n      return;\r\n    }\r\n\r\n    if (password.length < 6) {\r\n      setError(\"Password must be at least 6 characters\");\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n\r\n    try {\r\n      const result = await register(name, email, password, confirmPassword);\r\n      if (result.success) {\r\n        router.push(\"/\");\r\n      } else {\r\n        setError(result.message);\r\n      }\r\n    } catch (error: any) {\r\n      setError(error.message || \"Registration failed\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"max-w-md mx-auto mt-20 p-6 bg-white rounded shadow\">\r\n      <h1 className=\"text-2xl mb-6 font-semibold text-center\">Create Account</h1>\r\n\r\n      {error && <p className=\"text-red-500 mb-4\">{error}</p>}\r\n\r\n      <form onSubmit={handleSignup} className=\"space-y-4\">\r\n        <input\r\n          type=\"text\"\r\n          placeholder=\"Full Name\"\r\n          value={name}\r\n          onChange={(e) => setName(e.target.value)}\r\n          className=\"w-full border px-3 py-2 rounded\"\r\n          suppressHydrationWarning\r\n        />\r\n\r\n        <input\r\n          type=\"email\"\r\n          placeholder=\"Email\"\r\n          value={email}\r\n          onChange={(e) => setEmail(e.target.value)}\r\n          className=\"w-full border px-3 py-2 rounded\"\r\n          suppressHydrationWarning\r\n        />\r\n\r\n        <div className=\"relative\">\r\n          <input\r\n            type={showPassword ? \"text\" : \"password\"}\r\n            placeholder=\"Password\"\r\n            value={password}\r\n            onChange={(e) => setPassword(e.target.value)}\r\n            className=\"w-full border px-3 py-2 rounded\"\r\n            suppressHydrationWarning\r\n          />\r\n          <span\r\n            onClick={() => setShowPassword(!showPassword)}\r\n            className=\"absolute right-3 top-2.5 cursor-pointer text-sm text-gray-500\"\r\n          >\r\n            {showPassword ? \"Hide\" : \"Show\"}\r\n          </span>\r\n        </div>\r\n\r\n        <div className=\"relative\">\r\n          <input\r\n            type={showConfirmPassword ? \"text\" : \"password\"}\r\n            placeholder=\"Confirm Password\"\r\n            value={confirmPassword}\r\n            onChange={(e) => setConfirmPassword(e.target.value)}\r\n            className=\"w-full border px-3 py-2 rounded\"\r\n            suppressHydrationWarning\r\n          />\r\n          <span\r\n            onClick={() => setShowConfirmPassword(!showConfirmPassword)}\r\n            className=\"absolute right-3 top-2.5 cursor-pointer text-sm text-gray-500\"\r\n          >\r\n            {showConfirmPassword ? \"Hide\" : \"Show\"}\r\n          </span>\r\n        </div>\r\n\r\n        <button\r\n          type=\"submit\"\r\n          disabled={loading}\r\n          className={`w-full bg-orange-500 hover:bg-orange-600 text-white py-2 rounded font-semibold ${\r\n            loading ? 'opacity-50 cursor-not-allowed' : 'animate-bounce'\r\n          }`}\r\n          suppressHydrationWarning\r\n        >\r\n          {loading ? 'Creating Account...' : 'Create Account'}\r\n        </button>\r\n      </form>\r\n\r\n      <p className=\"mt-6 text-center text-sm text-gray-600\">\r\n        Already have an account?{\" \"}\r\n        <Link href=\"/login\" className=\"text-orange-500 hover:underline\">\r\n          Login\r\n        </Link>\r\n      </p>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,SAAS;QAET,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,mBAAmB,CAAC,MAAM;YACpD,SAAS;YACT;QACF;QAEA,IAAI,aAAa,iBAAiB;YAChC,SAAS;YACT;QACF;QAEA,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,SAAS;YACT;QACF;QAEA,WAAW;QAEX,IAAI;YACF,MAAM,SAAS,MAAM,SAAS,MAAM,OAAO,UAAU;YACrD,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,SAAS,OAAO,OAAO;YACzB;QACF,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,OAAO,IAAI;QAC5B,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAA0C;;;;;;YAEvD,uBAAS,8OAAC;gBAAE,WAAU;0BAAqB;;;;;;0BAE5C,8OAAC;gBAAK,UAAU;gBAAc,WAAU;;kCACtC,8OAAC;wBACC,MAAK;wBACL,aAAY;wBACZ,OAAO;wBACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;wBACvC,WAAU;wBACV,wBAAwB;;;;;;kCAG1B,8OAAC;wBACC,MAAK;wBACL,aAAY;wBACZ,OAAO;wBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wBACxC,WAAU;wBACV,wBAAwB;;;;;;kCAG1B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAM,eAAe,SAAS;gCAC9B,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gCAC3C,WAAU;gCACV,wBAAwB;;;;;;0CAE1B,8OAAC;gCACC,SAAS,IAAM,gBAAgB,CAAC;gCAChC,WAAU;0CAET,eAAe,SAAS;;;;;;;;;;;;kCAI7B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAM,sBAAsB,SAAS;gCACrC,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;gCAClD,WAAU;gCACV,wBAAwB;;;;;;0CAE1B,8OAAC;gCACC,SAAS,IAAM,uBAAuB,CAAC;gCACvC,WAAU;0CAET,sBAAsB,SAAS;;;;;;;;;;;;kCAIpC,8OAAC;wBACC,MAAK;wBACL,UAAU;wBACV,WAAW,CAAC,+EAA+E,EACzF,UAAU,kCAAkC,kBAC5C;wBACF,wBAAwB;kCAEvB,UAAU,wBAAwB;;;;;;;;;;;;0BAIvC,8OAAC;gBAAE,WAAU;;oBAAyC;oBAC3B;kCACzB,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAS,WAAU;kCAAkC;;;;;;;;;;;;;;;;;;AAMxE", "debugId": null}}]}