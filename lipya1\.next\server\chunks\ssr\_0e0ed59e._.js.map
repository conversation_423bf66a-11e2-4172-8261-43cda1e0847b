{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/app/submission/page.tsx"], "sourcesContent": ["// components/About.tsx\r\nimport React from 'react';\r\nimport {\r\n  FaSun,\r\n  FaLeaf,\r\n  FaSeedling,\r\n  FaOilCan\r\n} from 'react-icons/fa';\r\n\r\nexport default function About() {\r\n  return (\r\n    <div>\r\n      {/* صورة الهيرو */}\r\n      <div className=\"relative h-[300px] md:h-[400px] w-full\">\r\n        <img\r\n          src=\"/photo1.png\"\r\n          alt=\"About IREGO\"\r\n          className=\"object-cover w-full h-full\"\r\n        />\r\n        <div className=\"absolute inset-0 bg-black/50 flex items-center justify-center\">\r\n          <h1 className=\"text-3xl md:text-5xl font-bold text-white\">\r\n            SUBMISSION\r\n          </h1>\r\n        </div>\r\n      </div>\r\n\r\n\r\n\r\n  {/* مقدمة المؤتمر */}\r\n      <div className=\"mt-10 text-center px-4 max-w-3xl mx-auto\">\r\n        \r\n        <h2 className=\"text-lg md:text-2xl font-semibold\">\r\n         Sunmit Your{'  '} \r\n          <span className=\"text-orange-500 uppercase font-bold\">\r\n         Paper\r\n          </span>\r\n        </h2>\r\n\r\n        <p className=\"text-gray-700 text-sm md:text-base mb-8 max-w-2xl\">\r\n         We invite researchers, academics, and industry professionals to submit their original research papers for presentation at the International Renewable Energy, Gas & Oil, and Climate Change Conference. The conference provides a platform for sharing innovative ideas and fostering collaboration in the field of renewable energy and climate change.\r\n        </p>\r\n      </div>\r\n\r\n\r\n\r\n\r\n\r\n  {/* مقدمة المؤتمر */}\r\n      <div className=\"mt-10 text-center px-4 max-w-3xl mx-auto\">\r\n        <h2 className=\"text-lg md:text-2xl font-semibold\">\r\n        Submission {' '}\r\n          <span className=\"text-orange-500 uppercase font-bold\">\r\n  Process\r\n          </span>\r\n        </h2>\r\n      \r\n      </div>\r\n\r\n\r\n\r\n\r\n\r\n\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 p-4 mt-6\">\r\n          {/* كل كارت */}\r\n          <div className=\"bg-white rounded-2xl shadow-md p-4 text-center\">\r\n            <FaSun className=\"text-orange-500 text-4xl mx-auto mb-2\" />\r\n            <h2 className=\"text-orange-500 text-lg font-bold mb-3\">\r\n           Prepare Manuscript\r\n            </h2>\r\n            <ul className=\"text-gray-700 space-y-1 text-sm\">\r\n              <li>Follow IEEE formatting guidelines</li>\r\n      \r\n            </ul>\r\n          </div>\r\n\r\n          <div className=\"bg-white rounded-2xl shadow-md p-4 text-center\">\r\n            <FaLeaf className=\"text-orange-500 text-4xl mx-auto mb-2\" />\r\n            <h2 className=\"text-orange-500 text-lg font-bold mb-3\">\r\n            Review Content\r\n            </h2>\r\n            <ul className=\"text-gray-700 space-y-1 text-sm\">\r\n              <li>Ensure originality and quality</li>\r\n        \r\n            </ul>\r\n          </div>\r\n\r\n          <div className=\"bg-white rounded-2xl shadow-md p-4 text-center\">\r\n            <FaSeedling className=\"text-orange-500 text-4xl mx-auto mb-2\" />\r\n            <h2 className=\"text-orange-500 text-lg font-bold mb-3\">\r\n           Submit Online\r\n            </h2>\r\n            <ul className=\"text-gray-700 space-y-1 text-sm\">\r\n              <li>Upload through our system</li>\r\n          \r\n            </ul>\r\n          </div>\r\n\r\n          <div className=\"bg-white rounded-2xl shadow-md p-4 text-center\">\r\n            <FaOilCan className=\"text-orange-500 text-4xl mx-auto mb-2\" />\r\n            <h2 className=\"text-orange-500 text-lg font-bold mb-3\">\r\n            Track Status\r\n            </h2>\r\n            <ul className=\"text-gray-700 space-y-1 text-sm\">\r\n              <li>Monitor review progress</li>\r\n             \r\n            </ul>\r\n          </div>\r\n</div>\r\n\r\n\r\n<h2 className=\"text-2xl md:text-3xl font-bold text-center mt-16\">\r\n  <span className=\"text-orange-500\">Conference</span> Timeline\r\n</h2>\r\n<div className=\"mt-10 flex flex-row flex-wrap items-center justify-center gap-12 px-4 max-w-5xl mx-auto\">\r\n  {/* النقطة 1 */}\r\n  <div className=\"flex flex-col items-center min-w-[150px]\">\r\n    <div className=\"bg-orange-500 text-white text-sm px-4 py-1 rounded-full shadow-md mb-2\">\r\n      June 15, 2025\r\n    </div>\r\n    <div className=\"w-4 h-4 bg-orange-500 rounded-full shadow-lg mb-2\" />\r\n    <p className=\"text-center text-sm md:text-base font-medium\">Abstract Submission Deadline</p>\r\n  </div>\r\n\r\n  {/* النقطة 2 */}\r\n  <div className=\"flex flex-col items-center min-w-[150px]\">\r\n    <div className=\"w-4 h-4 bg-orange-500 rounded-full shadow-lg mb-2\" />\r\n    <p className=\"text-center text-sm md:text-base font-medium\">Notification of Acceptance</p>\r\n    <div className=\"bg-orange-500 text-white text-sm px-4 py-1 rounded-full shadow-md mt-2\">\r\n      August 15, 2025\r\n    </div>\r\n  </div>\r\n\r\n  {/* النقطة 3 */}\r\n  <div className=\"flex flex-col items-center min-w-[150px]\">\r\n    <div className=\"bg-orange-500 text-white text-sm px-4 py-1 rounded-full shadow-md mb-2\">\r\n      October 1, 2025\r\n    </div>\r\n    <div className=\"w-4 h-4 bg-orange-500 rounded-full shadow-lg mb-2\" />\r\n    <p className=\"text-center text-sm md:text-base font-medium\">Final Paper Submission</p>\r\n  </div>\r\n\r\n  {/* النقطة 4 */}\r\n  <div className=\"flex flex-col items-center min-w-[150px]\">\r\n    <div className=\"w-4 h-4 bg-orange-500 rounded-full shadow-lg mb-2\" />\r\n    <p className=\"text-center text-sm md:text-base font-medium\">Conference Dates</p>\r\n    <div className=\"bg-orange-500 text-white text-sm px-4 py-1 rounded-full shadow-md mt-2\">\r\n      November 25–27, 2025\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 p-4 mt-12\">\r\n\r\n  {/* القسم الأيمن - Review Process */}\r\n  <div>\r\n    <h2 className=\"text-xl md:text-2xl font-bold text-orange-500 mb-4\">\r\n      Review Process\r\n    </h2>\r\n    <div className=\"space-y-4 text-gray-700 text-sm md:text-base\">\r\n      <div className=\"flex items-start gap-2\">\r\n        <span className=\"font-bold text-orange-500\">1.</span>\r\n        <p>Manuscripts will undergo peer review by experts in the field.</p>\r\n      </div>\r\n      <div className=\"flex items-start gap-2\">\r\n        <span className=\"font-bold text-orange-500\">2.</span>\r\n        <p>Reviewers will evaluate originality, methodology, and contribution.</p>\r\n      </div>\r\n      <div className=\"flex items-start gap-2\">\r\n        <span className=\"font-bold text-orange-500\">3.</span>\r\n        <p>Authors will receive detailed feedback and suggestions.</p>\r\n      </div>\r\n      <div className=\"flex items-start gap-2\">\r\n        <span className=\"font-bold text-orange-500\">4.</span>\r\n        <p>Final decisions will be communicated within 4 weeks.</p>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  {/* القسم الأيسر - Publication and Indexing */}\r\n  <div>\r\n    <h2 className=\"text-xl md:text-2xl font-bold text-orange-500 mb-4\">\r\n      Publication and Indexing\r\n    </h2>\r\n    <div className=\"space-y-4 text-gray-700 text-sm md:text-base\">\r\n      <div className=\"flex items-start gap-2\">\r\n        <span className=\"text-green-600 font-bold\">✔</span>\r\n        <p>Accepted papers will be published in conference proceedings.</p>\r\n      </div>\r\n      <div className=\"flex items-start gap-2\">\r\n        <span className=\"text-green-600 font-bold\">✔</span>\r\n        <p>Proceedings will be indexed in major academic databases.</p>\r\n      </div>\r\n      <div className=\"flex items-start gap-2\">\r\n        <span className=\"text-green-600 font-bold\">✔</span>\r\n        <p>Selected papers may be invited for journal publication.</p>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n</div>\r\n\r\n <div className=\"mt-10 text-center px-4 max-w-3xl mx-auto\">\r\n \r\n        <p className=\"text-gray-700 text-sm md:text-base mb-8 max-w-2xl\">\r\nFor any questions about paper submission, please contact us at:        </p>\r\n      </div>\r\n\r\n\r\n <div className=\"mt-10 text-center px-4 max-w-3xl mx-auto\">\r\n        <h2 className=\"text-lg md:text-xl font-semibold\">\r\n    {' '}\r\n          <span className=\"text-orange-500 uppercase font-bold\">\r\n        <EMAIL>\r\n          </span>\r\n        </h2>\r\n       \r\n      </div>\r\n\r\n<div className=\"mt-10 flex justify-center\">\r\n  <a\r\n    href=\"https://cmt3.research.microsoft.com/User/Login?ReturnUrl=%2FIREGOCC2025\" // ← غيّر المسار حسب صفحتك الحقيقية\r\n    className=\"bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-md font-semibold transition flex items-center gap-2\"\r\n  >\r\n    <i className=\"fas fa-file-upload\"></i>\r\n Submit Your Paper\r\n  </a>\r\n</div>\r\n\r\n\r\n\r\n\r\n    </div>\r\n  );\r\n}\r\n\r\n"], "names": [], "mappings": "AAAA,uBAAuB;;;;;AAEvB;;;AAOe,SAAS;IACtB,qBACE,8OAAC;;0BAEC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,KAAI;wBACJ,KAAI;wBACJ,WAAU;;;;;;kCAEZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCAA4C;;;;;;;;;;;;;;;;;0BAS9D,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAG,WAAU;;4BAAoC;4BACrC;0CACX,8OAAC;gCAAK,WAAU;0CAAsC;;;;;;;;;;;;kCAKxD,8OAAC;wBAAE,WAAU;kCAAoD;;;;;;;;;;;;0BAUnE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;;wBAAoC;wBACtC;sCACV,8OAAC;4BAAK,WAAU;sCAAsC;;;;;;;;;;;;;;;;;0BAYxD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8IAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CAGvD,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC;8CAAG;;;;;;;;;;;;;;;;;kCAKR,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8IAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CAGvD,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC;8CAAG;;;;;;;;;;;;;;;;;kCAKR,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8IAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CAGvD,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC;8CAAG;;;;;;;;;;;;;;;;;kCAKR,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8IAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CAGvD,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC;8CAAG;;;;;;;;;;;;;;;;;;;;;;;0BAOlB,8OAAC;gBAAG,WAAU;;kCACZ,8OAAC;wBAAK,WAAU;kCAAkB;;;;;;oBAAiB;;;;;;;0BAErD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAyE;;;;;;0CAGxF,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;;;;;;;kCAI9D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAC5D,8OAAC;gCAAI,WAAU;0CAAyE;;;;;;;;;;;;kCAM1F,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAyE;;;;;;0CAGxF,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;;;;;;;kCAI9D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAC5D,8OAAC;gCAAI,WAAU;0CAAyE;;;;;;;;;;;;;;;;;;0BAM5F,8OAAC;gBAAI,WAAU;;kCAGb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAqD;;;;;;0CAGnE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAA4B;;;;;;0DAC5C,8OAAC;0DAAE;;;;;;;;;;;;kDAEL,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAA4B;;;;;;0DAC5C,8OAAC;0DAAE;;;;;;;;;;;;kDAEL,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAA4B;;;;;;0DAC5C,8OAAC;0DAAE;;;;;;;;;;;;kDAEL,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAA4B;;;;;;0DAC5C,8OAAC;0DAAE;;;;;;;;;;;;;;;;;;;;;;;;kCAMT,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAqD;;;;;;0CAGnE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAA2B;;;;;;0DAC3C,8OAAC;0DAAE;;;;;;;;;;;;kDAEL,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAA2B;;;;;;0DAC3C,8OAAC;0DAAE;;;;;;;;;;;;kDAEL,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAA2B;;;;;;0DAC3C,8OAAC;0DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOV,8OAAC;gBAAI,WAAU;0BAER,cAAA,8OAAC;oBAAE,WAAU;8BAAoD;;;;;;;;;;;0BAKxE,8OAAC;gBAAI,WAAU;0BACR,cAAA,8OAAC;oBAAG,WAAU;;wBACjB;sCACK,8OAAC;4BAAK,WAAU;sCAAsC;;;;;;;;;;;;;;;;;0BAOhE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,MAAK,0EAA0E,mCAAmC;;oBAClH,WAAU;;sCAEV,8OAAC;4BAAE,WAAU;;;;;;wBAAyB;;;;;;;;;;;;;;;;;;AAU1C", "debugId": null}}, {"offset": {"line": 781, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 819, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;AAAqC,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAMhG,8BAA8B;IAI5BI,SAASC;;;;;;;;;;;;AAIX,cAAc,0CAA0C,iBAAA;IAAE,MAAA,kBAAwB;AAAsB,EAAC,IAAA,OAAA;IAAA;IAAA;QAEzG,YAAA;YAAA;YAAA,iCAA4D;gBAC5D,OAAO,KAAA;oBAAMG;oBAAAA,EAAc,IAAIX,mBAAmB;qBAChDY,YAAY;8BACVC,IAAAA,CAAMZ,CAAAA;wBAAAA,KAAUa,GAAAA;4BAAAA,IAAQ;4BAAA;yBAAA;;uBACxBC,MAAM;;iBACNC,UAAU;sBACV,IAAA,CAAA;YAAA;SAAA,gCAA2C;;SAC3CC,YAAY;cACZC,IAAAA;YAAAA,IAAU,CAAA;YAAA;SAAA;cACVC,GAAAA;YAAAA,IAAU,EAAE;YAAA;SAAA;UACd,SAAA;YAAA,IAAA;YAAA;SAAA;UACAC,UAAU,CAAA;YAAA,IAAA;YAAA;SAAA;cACRC,OAAAA;YAAAA,IAAYnB;YAAAA;SAAAA;UACd,cAAA;YAAA,IAAA;YAAA;SAAA;IACF;CAAA,CAAE", "ignoreList": [0], "debugId": null}}]}