// API configuration and helper functions
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';

// Types
export interface User {
  id: number;
  name: string;
  email: string;
  email_verified_at?: string;
  created_at: string;
  updated_at: string;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  data?: {
    user: User;
    token: string;
    token_type: string;
    expires_in: number;
  };
}

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
}

// Helper function to get auth token from localStorage
const getAuthToken = (): string | null => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('auth_token');
  }
  return null;
};

// Helper function to set auth token in localStorage
const setAuthToken = (token: string): void => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('auth_token', token);
  }
};

// Helper function to remove auth token from localStorage
const removeAuthToken = (): void => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('auth_token');
  }
};

// Generic API call function
const apiCall = async <T = any>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> => {
  const token = getAuthToken();
  
  const defaultHeaders: HeadersInit = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  if (token) {
    defaultHeaders['Authorization'] = `Bearer ${token}`;
  }

  const config: RequestInit = {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
  };

  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, config);
    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'API request failed');
    }

    return data;
  } catch (error) {
    console.error('API Error:', error);
    throw error;
  }
};

// Authentication API calls
export const authAPI = {
  // Login
  login: async (email: string, password: string): Promise<AuthResponse> => {
    const response = await apiCall<AuthResponse['data']>('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });

    if (response.success && response.data?.token) {
      setAuthToken(response.data.token);
    }

    return response as AuthResponse;
  },

  // Register
  register: async (name: string, email: string, password: string, password_confirmation: string): Promise<AuthResponse> => {
    const response = await apiCall<AuthResponse['data']>('/auth/register', {
      method: 'POST',
      body: JSON.stringify({ 
        name, 
        email, 
        password, 
        password_confirmation 
      }),
    });

    if (response.success && response.data?.token) {
      setAuthToken(response.data.token);
    }

    return response as AuthResponse;
  },

  // Logout
  logout: async (): Promise<ApiResponse> => {
    try {
      const response = await apiCall('/auth/logout', {
        method: 'POST',
      });
      removeAuthToken();
      return response;
    } catch (error) {
      // Even if API call fails, remove token locally
      removeAuthToken();
      throw error;
    }
  },

  // Get current user
  me: async (): Promise<ApiResponse<User>> => {
    return apiCall<User>('/auth/me');
  },

  // Refresh token
  refresh: async (): Promise<AuthResponse> => {
    const response = await apiCall<AuthResponse['data']>('/auth/refresh', {
      method: 'POST',
    });

    if (response.success && response.data?.token) {
      setAuthToken(response.data.token);
    }

    return response as AuthResponse;
  },
};

// Conference API calls
export const conferenceAPI = {
  getAll: () => apiCall('/conferences'),
  getCurrent: () => apiCall('/conferences/current'),
  getById: (id: number) => apiCall(`/conferences/${id}`),
};

// Speaker API calls
export const speakerAPI = {
  getAll: () => apiCall('/speakers'),
  getById: (id: number) => apiCall(`/speakers/${id}`),
  getCommittees: () => apiCall('/speakers/committees/all'),
  getKeynotes: () => apiCall('/speakers/keynotes/all'),
};

// News API calls
export const newsAPI = {
  getAll: () => apiCall('/news'),
  getFeatured: () => apiCall('/news/featured'),
  getRecent: () => apiCall('/news/recent'),
  getBySlug: (slug: string) => apiCall(`/news/${slug}`),
};

// Sponsor API calls
export const sponsorAPI = {
  getAll: () => apiCall('/sponsors'),
  getById: (id: number) => apiCall(`/sponsors/${id}`),
};

// Event API calls
export const eventAPI = {
  getAll: () => apiCall('/events'),
  getById: (id: number) => apiCall(`/events/${id}`),
  getSchedule: () => apiCall('/events/schedule/all'),
};

// Registration API calls
export const registrationAPI = {
  create: (data: any) => apiCall('/registrations', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  getByConfirmationCode: (code: string) => apiCall(`/registrations/${code}`),
  checkStatus: (data: any) => apiCall('/registrations/check-status', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
};

// Export utility functions
export { getAuthToken, setAuthToken, removeAuthToken };
