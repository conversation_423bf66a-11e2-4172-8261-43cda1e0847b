{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/app/error.tsx"], "sourcesContent": ["\"use client\";\n\ninterface ErrorPageProps {\n  error: Error & { digest?: string };\n  reset: () => void;\n}\n\nexport default function ErrorPage({ reset }: Omit<ErrorPageProps, 'error'>) {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n      <div className=\"max-w-md w-full bg-white rounded-lg shadow-md p-6 text-center\">\n        <div className=\"text-red-500 text-6xl mb-4\">⚠️</div>\n        <h2 className=\"text-2xl font-bold text-gray-800 mb-2\">Something went wrong</h2>\n        <p className=\"text-gray-600 mb-4\">\n          We&apos;re sorry, but something unexpected happened. Please try again.\n        </p>\n        <div className=\"space-x-2\">\n          <button\n            onClick={reset}\n            className=\"bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded font-semibold\"\n          >\n            Try Again\n          </button>\n          <button\n            onClick={() => window.location.href = '/'}\n            className=\"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded font-semibold\"\n          >\n            Go Home\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAOe,SAAS,UAAU,EAAE,KAAK,EAAiC;IACxE,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BAA6B;;;;;;8BAC5C,6LAAC;oBAAG,WAAU;8BAAwC;;;;;;8BACtD,6LAAC;oBAAE,WAAU;8BAAqB;;;;;;8BAGlC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;sCAGD,6LAAC;4BACC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;4BACtC,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX;KA1BwB", "debugId": null}}]}