/* [project]/src/app/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after, ::backdrop {
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-border-style: solid;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
    }
  }
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.relative {
  position: relative;
}

.static {
  position: static;
}

.inset-0 {
  inset: calc(var(--spacing) * 0);
}

.top-2\.5 {
  top: calc(var(--spacing) * 2.5);
}

.top-4 {
  top: calc(var(--spacing) * 4);
}

.top-6 {
  top: calc(var(--spacing) * 6);
}

.top-\[35px\] {
  top: 35px;
}

.right-0 {
  right: calc(var(--spacing) * 0);
}

.right-3 {
  right: calc(var(--spacing) * 3);
}

.right-6 {
  right: calc(var(--spacing) * 6);
}

.left-0 {
  left: calc(var(--spacing) * 0);
}

.left-4 {
  left: calc(var(--spacing) * 4);
}

.z-0 {
  z-index: 0;
}

.z-10 {
  z-index: 10;
}

.z-20 {
  z-index: 20;
}

.z-30 {
  z-index: 30;
}

.z-40 {
  z-index: 40;
}

.z-50 {
  z-index: 50;
}

.mx-auto {
  margin-inline: auto;
}

.my-6 {
  margin-block: calc(var(--spacing) * 6);
}

.mt-1 {
  margin-top: calc(var(--spacing) * 1);
}

.mt-2 {
  margin-top: calc(var(--spacing) * 2);
}

.mt-4 {
  margin-top: calc(var(--spacing) * 4);
}

.mt-6 {
  margin-top: calc(var(--spacing) * 6);
}

.mt-8 {
  margin-top: calc(var(--spacing) * 8);
}

.mt-10 {
  margin-top: calc(var(--spacing) * 10);
}

.mt-12 {
  margin-top: calc(var(--spacing) * 12);
}

.mt-16 {
  margin-top: calc(var(--spacing) * 16);
}

.mt-20 {
  margin-top: calc(var(--spacing) * 20);
}

.mr-3 {
  margin-right: calc(var(--spacing) * 3);
}

.mb-2 {
  margin-bottom: calc(var(--spacing) * 2);
}

.mb-3 {
  margin-bottom: calc(var(--spacing) * 3);
}

.mb-4 {
  margin-bottom: calc(var(--spacing) * 4);
}

.mb-6 {
  margin-bottom: calc(var(--spacing) * 6);
}

.mb-8 {
  margin-bottom: calc(var(--spacing) * 8);
}

.mb-12 {
  margin-bottom: calc(var(--spacing) * 12);
}

.mb-16 {
  margin-bottom: calc(var(--spacing) * 16);
}

.ml-2 {
  margin-left: calc(var(--spacing) * 2);
}

.block {
  display: block;
}

.flex {
  display: flex;
}

.grid {
  display: grid;
}

.hidden {
  display: none;
}

.inline-block {
  display: inline-block;
}

.h-4 {
  height: calc(var(--spacing) * 4);
}

.h-6 {
  height: calc(var(--spacing) * 6);
}

.h-8 {
  height: calc(var(--spacing) * 8);
}

.h-10 {
  height: calc(var(--spacing) * 10);
}

.h-12 {
  height: calc(var(--spacing) * 12);
}

.h-14 {
  height: calc(var(--spacing) * 14);
}

.h-16 {
  height: calc(var(--spacing) * 16);
}

.h-20 {
  height: calc(var(--spacing) * 20);
}

.h-24 {
  height: calc(var(--spacing) * 24);
}

.h-40 {
  height: calc(var(--spacing) * 40);
}

.h-46 {
  height: calc(var(--spacing) * 46);
}

.h-64 {
  height: calc(var(--spacing) * 64);
}

.h-\[2px\] {
  height: 2px;
}

.h-\[300px\] {
  height: 300px;
}

.h-\[500px\] {
  height: 500px;
}

.h-auto {
  height: auto;
}

.h-full {
  height: 100%;
}

.min-h-screen {
  min-height: 100vh;
}

.w-3 {
  width: calc(var(--spacing) * 3);
}

.w-4 {
  width: calc(var(--spacing) * 4);
}

.w-6 {
  width: calc(var(--spacing) * 6);
}

.w-8 {
  width: calc(var(--spacing) * 8);
}

.w-10 {
  width: calc(var(--spacing) * 10);
}

.w-12 {
  width: calc(var(--spacing) * 12);
}

.w-14 {
  width: calc(var(--spacing) * 14);
}

.w-20 {
  width: calc(var(--spacing) * 20);
}

.w-24 {
  width: calc(var(--spacing) * 24);
}

.w-40 {
  width: calc(var(--spacing) * 40);
}

.w-66 {
  width: calc(var(--spacing) * 66);
}

.w-\[250px\] {
  width: 250px;
}

.w-auto {
  width: auto;
}

.w-fit {
  width: fit-content;
}

.w-full {
  width: 100%;
}

.w-max {
  width: max-content;
}

.max-w-2xl {
  max-width: var(--container-2xl);
}

.max-w-3xl {
  max-width: var(--container-3xl);
}

.max-w-4xl {
  max-width: var(--container-4xl);
}

.max-w-5xl {
  max-width: var(--container-5xl);
}

.max-w-6xl {
  max-width: var(--container-6xl);
}

.max-w-7xl {
  max-width: var(--container-7xl);
}

.max-w-\[120px\] {
  max-width: 120px;
}

.max-w-\[150px\] {
  max-width: 150px;
}

.max-w-\[220px\] {
  max-width: 220px;
}

.max-w-\[1000px\] {
  max-width: 1000px;
}

.max-w-md {
  max-width: var(--container-md);
}

.max-w-xl {
  max-width: var(--container-xl);
}

.min-w-\[60px\] {
  min-width: 60px;
}

.min-w-\[100px\] {
  min-width: 100px;
}

.min-w-\[140px\] {
  min-width: 140px;
}

.min-w-\[150px\] {
  min-width: 150px;
}

.flex-grow {
  flex-grow: 1;
}

.border-collapse {
  border-collapse: collapse;
}

.transform {
  transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
}

.animate-bounce {
  animation: var(--animate-bounce);
}

.animate-spin {
  animation: var(--animate-spin);
}

.cursor-pointer {
  cursor: pointer;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.justify-end {
  justify-content: flex-end;
}

.gap-2 {
  gap: calc(var(--spacing) * 2);
}

.gap-3 {
  gap: calc(var(--spacing) * 3);
}

.gap-4 {
  gap: calc(var(--spacing) * 4);
}

.gap-6 {
  gap: calc(var(--spacing) * 6);
}

.gap-8 {
  gap: calc(var(--spacing) * 8);
}

.gap-10 {
  gap: calc(var(--spacing) * 10);
}

.gap-12 {
  gap: calc(var(--spacing) * 12);
}

:where(.space-y-1 > :not(:last-child)) {
  --tw-space-y-reverse: 0;
  margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
  margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
}

:where(.space-y-2 > :not(:last-child)) {
  --tw-space-y-reverse: 0;
  margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
  margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
}

:where(.space-y-4 > :not(:last-child)) {
  --tw-space-y-reverse: 0;
  margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
  margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
}

:where(.space-y-6 > :not(:last-child)) {
  --tw-space-y-reverse: 0;
  margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
  margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
}

:where(.space-x-2 > :not(:last-child)) {
  --tw-space-x-reverse: 0;
  margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
  margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
}

:where(.space-x-3 > :not(:last-child)) {
  --tw-space-x-reverse: 0;
  margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
  margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
}

.overflow-hidden {
  overflow: hidden;
}

.rounded {
  border-radius: .25rem;
}

.rounded-2xl {
  border-radius: var(--radius-2xl);
}

.rounded-full {
  border-radius: 3.40282e38px;
}

.rounded-lg {
  border-radius: var(--radius-lg);
}

.rounded-md {
  border-radius: var(--radius-md);
}

.rounded-xl {
  border-radius: var(--radius-xl);
}

.border {
  border-style: var(--tw-border-style);
  border-width: 1px;
}

.border-4 {
  border-style: var(--tw-border-style);
  border-width: 4px;
}

.border-t {
  border-top-style: var(--tw-border-style);
  border-top-width: 1px;
}

.border-t-4 {
  border-top-style: var(--tw-border-style);
  border-top-width: 4px;
}

.border-b {
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 1px;
}

.border-gray-200 {
  border-color: var(--color-gray-200);
}

.border-gray-300 {
  border-color: var(--color-gray-300);
}

.border-gray-700 {
  border-color: var(--color-gray-700);
}

.border-orange-200 {
  border-color: var(--color-orange-200);
}

.border-orange-300 {
  border-color: var(--color-orange-300);
}

.border-orange-500 {
  border-color: var(--color-orange-500);
}

.border-white {
  border-color: var(--color-white);
}

.border-white\/10 {
  border-color: #ffffff1a;
}

@supports (color: color-mix(in lab, red, red)) {
  .border-white\/10 {
    border-color: color-mix(in oklab, var(--color-white) 10%, transparent);
  }
}

.border-t-orange-500 {
  border-top-color: var(--color-orange-500);
}

.bg-black\/40 {
  background-color: #0006;
}

@supports (color: color-mix(in lab, red, red)) {
  .bg-black\/40 {
    background-color: color-mix(in oklab, var(--color-black) 40%, transparent);
  }
}

.bg-black\/50 {
  background-color: #00000080;
}

@supports (color: color-mix(in lab, red, red)) {
  .bg-black\/50 {
    background-color: color-mix(in oklab, var(--color-black) 50%, transparent);
  }
}

.bg-gray-50 {
  background-color: var(--color-gray-50);
}

.bg-gray-100 {
  background-color: var(--color-gray-100);
}

.bg-gray-200 {
  background-color: var(--color-gray-200);
}

.bg-gray-500 {
  background-color: var(--color-gray-500);
}

.bg-gray-800\/30 {
  background-color: #1e29394d;
}

@supports (color: color-mix(in lab, red, red)) {
  .bg-gray-800\/30 {
    background-color: color-mix(in oklab, var(--color-gray-800) 30%, transparent);
  }
}

.bg-gray-900 {
  background-color: var(--color-gray-900);
}

.bg-orange-50 {
  background-color: var(--color-orange-50);
}

.bg-orange-100 {
  background-color: var(--color-orange-100);
}

.bg-orange-400 {
  background-color: var(--color-orange-400);
}

.bg-orange-500 {
  background-color: var(--color-orange-500);
}

.bg-orange-600 {
  background-color: var(--color-orange-600);
}

.bg-white {
  background-color: var(--color-white);
}

.object-contain {
  object-fit: contain;
}

.object-cover {
  object-fit: cover;
}

.p-2 {
  padding: calc(var(--spacing) * 2);
}

.p-3 {
  padding: calc(var(--spacing) * 3);
}

.p-4 {
  padding: calc(var(--spacing) * 4);
}

.p-6 {
  padding: calc(var(--spacing) * 6);
}

.p-8 {
  padding: calc(var(--spacing) * 8);
}

.px-2 {
  padding-inline: calc(var(--spacing) * 2);
}

.px-3 {
  padding-inline: calc(var(--spacing) * 3);
}

.px-4 {
  padding-inline: calc(var(--spacing) * 4);
}

.px-6 {
  padding-inline: calc(var(--spacing) * 6);
}

.py-1 {
  padding-block: calc(var(--spacing) * 1);
}

.py-1\.5 {
  padding-block: calc(var(--spacing) * 1.5);
}

.py-2 {
  padding-block: calc(var(--spacing) * 2);
}

.py-3 {
  padding-block: calc(var(--spacing) * 3);
}

.py-10 {
  padding-block: calc(var(--spacing) * 10);
}

.py-12 {
  padding-block: calc(var(--spacing) * 12);
}

.py-16 {
  padding-block: calc(var(--spacing) * 16);
}

.pt-2 {
  padding-top: calc(var(--spacing) * 2);
}

.pb-4 {
  padding-bottom: calc(var(--spacing) * 4);
}

.pb-12 {
  padding-bottom: calc(var(--spacing) * 12);
}

.pb-16 {
  padding-bottom: calc(var(--spacing) * 16);
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-2xl {
  font-size: var(--text-2xl);
  line-height: var(--tw-leading, var(--text-2xl--line-height));
}

.text-3xl {
  font-size: var(--text-3xl);
  line-height: var(--tw-leading, var(--text-3xl--line-height));
}

.text-4xl {
  font-size: var(--text-4xl);
  line-height: var(--tw-leading, var(--text-4xl--line-height));
}

.text-6xl {
  font-size: var(--text-6xl);
  line-height: var(--tw-leading, var(--text-6xl--line-height));
}

.text-base {
  font-size: var(--text-base);
  line-height: var(--tw-leading, var(--text-base--line-height));
}

.text-lg {
  font-size: var(--text-lg);
  line-height: var(--tw-leading, var(--text-lg--line-height));
}

.text-sm {
  font-size: var(--text-sm);
  line-height: var(--tw-leading, var(--text-sm--line-height));
}

.text-xl {
  font-size: var(--text-xl);
  line-height: var(--tw-leading, var(--text-xl--line-height));
}

.text-xs {
  font-size: var(--text-xs);
  line-height: var(--tw-leading, var(--text-xs--line-height));
}

.leading-relaxed {
  --tw-leading: var(--leading-relaxed);
  line-height: var(--leading-relaxed);
}

.leading-snug {
  --tw-leading: var(--leading-snug);
  line-height: var(--leading-snug);
}

.font-bold {
  --tw-font-weight: var(--font-weight-bold);
  font-weight: var(--font-weight-bold);
}

.font-medium {
  --tw-font-weight: var(--font-weight-medium);
  font-weight: var(--font-weight-medium);
}

.font-semibold {
  --tw-font-weight: var(--font-weight-semibold);
  font-weight: var(--font-weight-semibold);
}

.whitespace-nowrap {
  white-space: nowrap;
}

.text-black {
  color: var(--color-black);
}

.text-blue-600 {
  color: var(--color-blue-600);
}

.text-blue-900 {
  color: var(--color-blue-900);
}

.text-gray-500 {
  color: var(--color-gray-500);
}

.text-gray-600 {
  color: var(--color-gray-600);
}

.text-gray-700 {
  color: var(--color-gray-700);
}

.text-gray-800 {
  color: var(--color-gray-800);
}

.text-gray-900 {
  color: var(--color-gray-900);
}

.text-green-600 {
  color: var(--color-green-600);
}

.text-orange-500 {
  color: var(--color-orange-500);
}

.text-orange-600 {
  color: var(--color-orange-600);
}

.text-orange-800 {
  color: var(--color-orange-800);
}

.text-red-500 {
  color: var(--color-red-500);
}

.text-white {
  color: var(--color-white);
}

.uppercase {
  text-transform: uppercase;
}

.underline {
  text-decoration-line: underline;
}

.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.shadow {
  --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

.shadow-lg {
  --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

.shadow-md {
  --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

.shadow-sm {
  --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

.backdrop-blur-md {
  --tw-backdrop-blur: blur(var(--blur-md));
  backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
}

.transition {
  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
}

.transition-all {
  transition-property: all;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
}

.transition-colors {
  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
}

.transition-transform {
  transition-property: transform, translate, scale, rotate;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
}

.duration-300 {
  --tw-duration: .3s;
  transition-duration: .3s;
}

.duration-700 {
  --tw-duration: .7s;
  transition-duration: .7s;
}

.ease-in-out {
  --tw-ease: var(--ease-in-out);
  transition-timing-function: var(--ease-in-out);
}

.select-text {
  -webkit-user-select: text;
  user-select: text;
}

@media (hover: hover) {
  .hover\:scale-\[1\.02\]:hover {
    scale: 1.02;
  }
}

@media (hover: hover) {
  .hover\:bg-gray-100:hover {
    background-color: var(--color-gray-100);
  }
}

@media (hover: hover) {
  .hover\:bg-gray-600:hover {
    background-color: var(--color-gray-600);
  }
}

@media (hover: hover) {
  .hover\:bg-orange-600:hover {
    background-color: var(--color-orange-600);
  }
}

@media (hover: hover) {
  .hover\:bg-orange-700:hover {
    background-color: var(--color-orange-700);
  }
}

@media (hover: hover) {
  .hover\:text-blue-800:hover {
    color: var(--color-blue-800);
  }
}

@media (hover: hover) {
  .hover\:text-orange-400:hover {
    color: var(--color-orange-400);
  }
}

@media (hover: hover) {
  .hover\:text-orange-500:hover {
    color: var(--color-orange-500);
  }
}

@media (hover: hover) {
  .hover\:text-orange-700:hover {
    color: var(--color-orange-700);
  }
}

@media (hover: hover) {
  .hover\:text-white:hover {
    color: var(--color-white);
  }
}

@media (hover: hover) {
  .hover\:underline:hover {
    text-decoration-line: underline;
  }
}

@media (hover: hover) {
  .hover\:shadow-md:hover {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
}

.focus\:outline-none:focus {
  --tw-outline-style: none;
  outline-style: none;
}

@media (width >= 40rem) {
  .sm\:h-\[550px\] {
    height: 550px;
  }
}

@media (width >= 40rem) {
  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (width >= 40rem) {
  .sm\:flex-row {
    flex-direction: row;
  }
}

@media (width >= 40rem) {
  .sm\:px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }
}

@media (width >= 40rem) {
  .sm\:text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
}

@media (width >= 48rem) {
  .md\:flex {
    display: flex;
  }
}

@media (width >= 48rem) {
  .md\:hidden {
    display: none;
  }
}

@media (width >= 48rem) {
  .md\:h-80 {
    height: calc(var(--spacing) * 80);
  }
}

@media (width >= 48rem) {
  .md\:h-\[400px\] {
    height: 400px;
  }
}

@media (width >= 48rem) {
  .md\:h-\[600px\] {
    height: 600px;
  }
}

@media (width >= 48rem) {
  .md\:w-1\/2 {
    width: 50%;
  }
}

@media (width >= 48rem) {
  .md\:w-\[300px\] {
    width: 300px;
  }
}

@media (width >= 48rem) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (width >= 48rem) {
  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

@media (width >= 48rem) {
  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

@media (width >= 48rem) {
  .md\:flex-row {
    flex-direction: row;
  }
}

@media (width >= 48rem) {
  .md\:px-20 {
    padding-inline: calc(var(--spacing) * 20);
  }
}

@media (width >= 48rem) {
  .md\:text-left {
    text-align: left;
  }
}

@media (width >= 48rem) {
  .md\:text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }
}

@media (width >= 48rem) {
  .md\:text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }
}

@media (width >= 48rem) {
  .md\:text-5xl {
    font-size: var(--text-5xl);
    line-height: var(--tw-leading, var(--text-5xl--line-height));
  }
}

@media (width >= 48rem) {
  .md\:text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
}

@media (width >= 48rem) {
  .md\:text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
}

@media (width >= 48rem) {
  .md\:text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
}

@media (width >= 64rem) {
  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

@media (width >= 64rem) {
  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

@media (width >= 64rem) {
  .lg\:px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }
}

@media (width >= 64rem) {
  .lg\:px-12 {
    padding-inline: calc(var(--spacing) * 12);
  }
}

@layer theme {
  :root, :host {
    --color-red-500: oklch(63.7% .237 25.331);
    --color-orange-50: oklch(98% .016 73.684);
    --color-orange-100: oklch(95.4% .038 75.164);
    --color-orange-200: oklch(90.1% .076 70.697);
    --color-orange-300: oklch(83.7% .128 66.29);
    --color-orange-400: oklch(75% .183 55.934);
    --color-orange-500: oklch(70.5% .213 47.604);
    --color-orange-600: oklch(64.6% .222 41.116);
    --color-orange-700: oklch(55.3% .195 38.402);
    --color-orange-800: oklch(47% .157 37.304);
    --color-green-600: oklch(62.7% .194 149.214);
    --color-blue-600: oklch(54.6% .245 262.881);
    --color-blue-800: oklch(42.4% .199 265.638);
    --color-blue-900: oklch(37.9% .146 265.522);
    --color-gray-50: oklch(98.5% .002 247.839);
    --color-gray-100: oklch(96.7% .003 264.542);
    --color-gray-200: oklch(92.8% .006 264.531);
    --color-gray-300: oklch(87.2% .01 258.338);
    --color-gray-500: oklch(55.1% .027 264.364);
    --color-gray-600: oklch(44.6% .03 256.802);
    --color-gray-700: oklch(37.3% .034 259.733);
    --color-gray-800: oklch(27.8% .033 256.848);
    --color-gray-900: oklch(21% .034 264.665);
    --color-black: #000;
    --color-white: #fff;
    --spacing: .25rem;
    --container-md: 28rem;
    --container-xl: 36rem;
    --container-2xl: 42rem;
    --container-3xl: 48rem;
    --container-4xl: 56rem;
    --container-5xl: 64rem;
    --container-6xl: 72rem;
    --container-7xl: 80rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --leading-snug: 1.375;
    --leading-relaxed: 1.625;
    --radius-md: .375rem;
    --radius-lg: .5rem;
    --radius-xl: .75rem;
    --radius-2xl: 1rem;
    --ease-in-out: cubic-bezier(.4, 0, .2, 1);
    --animate-spin: spin 1s linear infinite;
    --animate-bounce: bounce 1s infinite;
    --blur-md: 12px;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-geist-sans);
    --default-mono-font-family: var(--font-geist-mono);
  }
}

@layer base {
  *, :after, :before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }

  ::file-selector-button {
    margin-inline-end: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }

  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}

@layer components, utilities;

.fa {
  font-family: var(--fa-style-family, "Font Awesome 6 Free");
  font-weight: var(--fa-style, 900);
}

.fa, .fa-brands, .fa-regular, .fa-solid, .fab, .far, .fas {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: var(--fa-display, inline-block);
  font-variant: normal;
  text-rendering: auto;
  font-style: normal;
  line-height: 1;
}

.fa-brands:before, .fa-regular:before, .fa-solid:before, .fa:before, .fab:before, .far:before, .fas:before {
  content: var(--fa);
}

.fa-classic, .fa-regular, .fa-solid, .far, .fas {
  font-family: "Font Awesome 6 Free";
}

.fa-brands, .fab {
  font-family: "Font Awesome 6 Brands";
}

.fa-1x {
  font-size: 1em;
}

.fa-2x {
  font-size: 2em;
}

.fa-3x {
  font-size: 3em;
}

.fa-4x {
  font-size: 4em;
}

.fa-5x {
  font-size: 5em;
}

.fa-6x {
  font-size: 6em;
}

.fa-7x {
  font-size: 7em;
}

.fa-8x {
  font-size: 8em;
}

.fa-9x {
  font-size: 9em;
}

.fa-10x {
  font-size: 10em;
}

.fa-2xs {
  vertical-align: .225em;
  font-size: .625em;
  line-height: .1em;
}

.fa-xs {
  vertical-align: .125em;
  font-size: .75em;
  line-height: .08333em;
}

.fa-sm {
  vertical-align: .05357em;
  font-size: .875em;
  line-height: .07143em;
}

.fa-lg {
  vertical-align: -.075em;
  font-size: 1.25em;
  line-height: .05em;
}

.fa-xl {
  vertical-align: -.125em;
  font-size: 1.5em;
  line-height: .04167em;
}

.fa-2xl {
  vertical-align: -.1875em;
  font-size: 2em;
  line-height: .03125em;
}

.fa-fw {
  text-align: center;
  width: 1.25em;
}

.fa-ul {
  margin-left: var(--fa-li-margin, 2.5em);
  padding-left: 0;
  list-style-type: none;
}

.fa-ul > li {
  position: relative;
}

.fa-li {
  left: calc(var(--fa-li-width, 2em) * -1);
  text-align: center;
  width: var(--fa-li-width, 2em);
  line-height: inherit;
  position: absolute;
}

.fa-border {
  border-radius: var(--fa-border-radius, .1em);
  border: var(--fa-border-width, .08em) var(--fa-border-style, solid) var(--fa-border-color, #eee);
  padding: var(--fa-border-padding, .2em .25em .15em);
}

.fa-pull-left {
  float: left;
  margin-right: var(--fa-pull-margin, .3em);
}

.fa-pull-right {
  float: right;
  margin-left: var(--fa-pull-margin, .3em);
}

.fa-beat {
  animation-name: fa-beat;
  animation-delay: var(--fa-animation-delay, 0s);
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 1s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, ease-in-out);
}

.fa-bounce {
  animation-name: fa-bounce;
  animation-delay: var(--fa-animation-delay, 0s);
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 1s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, cubic-bezier(.28, .84, .42, 1));
}

.fa-fade {
  animation-name: fa-fade;
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, cubic-bezier(.4, 0, .6, 1));
}

.fa-beat-fade, .fa-fade {
  animation-delay: var(--fa-animation-delay, 0s);
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 1s);
}

.fa-beat-fade {
  animation-name: fa-beat-fade;
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, cubic-bezier(.4, 0, .6, 1));
}

.fa-flip {
  animation-name: fa-flip;
  animation-delay: var(--fa-animation-delay, 0s);
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 1s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, ease-in-out);
}

.fa-shake {
  animation-name: fa-shake;
  animation-duration: var(--fa-animation-duration, 1s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, linear);
}

.fa-shake, .fa-spin {
  animation-delay: var(--fa-animation-delay, 0s);
  animation-direction: var(--fa-animation-direction, normal);
}

.fa-spin {
  animation-name: fa-spin;
  animation-duration: var(--fa-animation-duration, 2s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, linear);
}

.fa-spin-reverse {
  --fa-animation-direction: reverse;
}

.fa-pulse, .fa-spin-pulse {
  animation-name: fa-spin;
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 1s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, steps(8));
}

@media (prefers-reduced-motion: reduce) {
  .fa-beat, .fa-beat-fade, .fa-bounce, .fa-fade, .fa-flip, .fa-pulse, .fa-shake, .fa-spin, .fa-spin-pulse {
    transition-duration: 0s;
    transition-delay: 0s;
    animation-duration: 1ms;
    animation-iteration-count: 1;
    animation-delay: -1ms;
  }
}

@keyframes fa-beat {
  0%, 90% {
    transform: scale(1);
  }

  45% {
    transform: scale(var(--fa-beat-scale, 1.25));
  }
}

@keyframes fa-bounce {
  0% {
    transform: scale(1)translateY(0);
  }

  10% {
    transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, .9)) translateY(0);
  }

  30% {
    transform: scale(var(--fa-bounce-jump-scale-x, .9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -.5em));
  }

  50% {
    transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, .95)) translateY(0);
  }

  57% {
    transform: scale(1) translateY(var(--fa-bounce-rebound, -.125em));
  }

  64% {
    transform: scale(1)translateY(0);
  }

  to {
    transform: scale(1)translateY(0);
  }
}

@keyframes fa-fade {
  50% {
    opacity: var(--fa-fade-opacity, .4);
  }
}

@keyframes fa-beat-fade {
  0%, to {
    opacity: var(--fa-beat-fade-opacity, .4);
    transform: scale(1);
  }

  50% {
    opacity: 1;
    transform: scale(var(--fa-beat-fade-scale, 1.125));
  }
}

@keyframes fa-flip {
  50% {
    transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));
  }
}

@keyframes fa-shake {
  0% {
    transform: rotate(-15deg);
  }

  4% {
    transform: rotate(15deg);
  }

  8%, 24% {
    transform: rotate(-18deg);
  }

  12%, 28% {
    transform: rotate(18deg);
  }

  16% {
    transform: rotate(-22deg);
  }

  20% {
    transform: rotate(22deg);
  }

  32% {
    transform: rotate(-12deg);
  }

  36% {
    transform: rotate(12deg);
  }

  40%, to {
    transform: rotate(0);
  }
}

@keyframes fa-spin {
  0% {
    transform: rotate(0);
  }

  to {
    transform: rotate(1turn);
  }
}

.fa-rotate-90 {
  transform: rotate(90deg);
}

.fa-rotate-180 {
  transform: rotate(180deg);
}

.fa-rotate-270 {
  transform: rotate(270deg);
}

.fa-flip-horizontal {
  transform: scaleX(-1);
}

.fa-flip-vertical {
  transform: scaleY(-1);
}

.fa-flip-both, .fa-flip-horizontal.fa-flip-vertical {
  transform: scale(-1);
}

.fa-rotate-by {
  transform: rotate(var(--fa-rotate-angle, 0));
}

.fa-stack {
  vertical-align: middle;
  width: 2.5em;
  height: 2em;
  line-height: 2em;
  display: inline-block;
  position: relative;
}

.fa-stack-1x, .fa-stack-2x {
  text-align: center;
  width: 100%;
  z-index: var(--fa-stack-z-index, auto);
  position: absolute;
  left: 0;
}

.fa-stack-1x {
  line-height: inherit;
}

.fa-stack-2x {
  font-size: 2em;
}

.fa-inverse {
  color: var(--fa-inverse, #fff);
}

.fa-0 {
  --fa: "0";
}

.fa-1 {
  --fa: "1";
}

.fa-2 {
  --fa: "2";
}

.fa-3 {
  --fa: "3";
}

.fa-4 {
  --fa: "4";
}

.fa-5 {
  --fa: "5";
}

.fa-6 {
  --fa: "6";
}

.fa-7 {
  --fa: "7";
}

.fa-8 {
  --fa: "8";
}

.fa-9 {
  --fa: "9";
}

.fa-fill-drip {
  --fa: "";
}

.fa-arrows-to-circle {
  --fa: "";
}

.fa-chevron-circle-right, .fa-circle-chevron-right {
  --fa: "";
}

.fa-at {
  --fa: "@";
}

.fa-trash-alt, .fa-trash-can {
  --fa: "";
}

.fa-text-height {
  --fa: "";
}

.fa-user-times, .fa-user-xmark {
  --fa: "";
}

.fa-stethoscope {
  --fa: "";
}

.fa-comment-alt, .fa-message {
  --fa: "";
}

.fa-info {
  --fa: "";
}

.fa-compress-alt, .fa-down-left-and-up-right-to-center {
  --fa: "";
}

.fa-explosion {
  --fa: "";
}

.fa-file-alt, .fa-file-lines, .fa-file-text {
  --fa: "";
}

.fa-wave-square {
  --fa: "";
}

.fa-ring {
  --fa: "";
}

.fa-building-un {
  --fa: "";
}

.fa-dice-three {
  --fa: "";
}

.fa-calendar-alt, .fa-calendar-days {
  --fa: "";
}

.fa-anchor-circle-check {
  --fa: "";
}

.fa-building-circle-arrow-right {
  --fa: "";
}

.fa-volleyball, .fa-volleyball-ball {
  --fa: "";
}

.fa-arrows-up-to-line {
  --fa: "";
}

.fa-sort-desc, .fa-sort-down {
  --fa: "";
}

.fa-circle-minus, .fa-minus-circle {
  --fa: "";
}

.fa-door-open {
  --fa: "";
}

.fa-right-from-bracket, .fa-sign-out-alt {
  --fa: "";
}

.fa-atom {
  --fa: "";
}

.fa-soap {
  --fa: "";
}

.fa-heart-music-camera-bolt, .fa-icons {
  --fa: "";
}

.fa-microphone-alt-slash, .fa-microphone-lines-slash {
  --fa: "";
}

.fa-bridge-circle-check {
  --fa: "";
}

.fa-pump-medical {
  --fa: "";
}

.fa-fingerprint {
  --fa: "";
}

.fa-hand-point-right {
  --fa: "";
}

.fa-magnifying-glass-location, .fa-search-location {
  --fa: "";
}

.fa-forward-step, .fa-step-forward {
  --fa: "";
}

.fa-face-smile-beam, .fa-smile-beam {
  --fa: "";
}

.fa-flag-checkered {
  --fa: "";
}

.fa-football, .fa-football-ball {
  --fa: "";
}

.fa-school-circle-exclamation {
  --fa: "";
}

.fa-crop {
  --fa: "";
}

.fa-angle-double-down, .fa-angles-down {
  --fa: "";
}

.fa-users-rectangle {
  --fa: "";
}

.fa-people-roof {
  --fa: "";
}

.fa-people-line {
  --fa: "";
}

.fa-beer, .fa-beer-mug-empty {
  --fa: "";
}

.fa-diagram-predecessor {
  --fa: "";
}

.fa-arrow-up-long, .fa-long-arrow-up {
  --fa: "";
}

.fa-burn, .fa-fire-flame-simple {
  --fa: "";
}

.fa-male, .fa-person {
  --fa: "";
}

.fa-laptop {
  --fa: "";
}

.fa-file-csv {
  --fa: "";
}

.fa-menorah {
  --fa: "";
}

.fa-truck-plane {
  --fa: "";
}

.fa-record-vinyl {
  --fa: "";
}

.fa-face-grin-stars, .fa-grin-stars {
  --fa: "";
}

.fa-bong {
  --fa: "";
}

.fa-pastafarianism, .fa-spaghetti-monster-flying {
  --fa: "";
}

.fa-arrow-down-up-across-line {
  --fa: "";
}

.fa-spoon, .fa-utensil-spoon {
  --fa: "";
}

.fa-jar-wheat {
  --fa: "";
}

.fa-envelopes-bulk, .fa-mail-bulk {
  --fa: "";
}

.fa-file-circle-exclamation {
  --fa: "";
}

.fa-circle-h, .fa-hospital-symbol {
  --fa: "";
}

.fa-pager {
  --fa: "";
}

.fa-address-book, .fa-contact-book {
  --fa: "";
}

.fa-strikethrough {
  --fa: "";
}

.fa-k {
  --fa: "K";
}

.fa-landmark-flag {
  --fa: "";
}

.fa-pencil, .fa-pencil-alt {
  --fa: "";
}

.fa-backward {
  --fa: "";
}

.fa-caret-right {
  --fa: "";
}

.fa-comments {
  --fa: "";
}

.fa-file-clipboard, .fa-paste {
  --fa: "";
}

.fa-code-pull-request {
  --fa: "";
}

.fa-clipboard-list {
  --fa: "";
}

.fa-truck-loading, .fa-truck-ramp-box {
  --fa: "";
}

.fa-user-check {
  --fa: "";
}

.fa-vial-virus {
  --fa: "";
}

.fa-sheet-plastic {
  --fa: "";
}

.fa-blog {
  --fa: "";
}

.fa-user-ninja {
  --fa: "";
}

.fa-person-arrow-up-from-line {
  --fa: "";
}

.fa-scroll-torah, .fa-torah {
  --fa: "";
}

.fa-broom-ball, .fa-quidditch, .fa-quidditch-broom-ball {
  --fa: "";
}

.fa-toggle-off {
  --fa: "";
}

.fa-archive, .fa-box-archive {
  --fa: "";
}

.fa-person-drowning {
  --fa: "";
}

.fa-arrow-down-9-1, .fa-sort-numeric-desc, .fa-sort-numeric-down-alt {
  --fa: "";
}

.fa-face-grin-tongue-squint, .fa-grin-tongue-squint {
  --fa: "";
}

.fa-spray-can {
  --fa: "";
}

.fa-truck-monster {
  --fa: "";
}

.fa-w {
  --fa: "W";
}

.fa-earth-africa, .fa-globe-africa {
  --fa: "";
}

.fa-rainbow {
  --fa: "";
}

.fa-circle-notch {
  --fa: "";
}

.fa-tablet-alt, .fa-tablet-screen-button {
  --fa: "";
}

.fa-paw {
  --fa: "";
}

.fa-cloud {
  --fa: "";
}

.fa-trowel-bricks {
  --fa: "";
}

.fa-face-flushed, .fa-flushed {
  --fa: "";
}

.fa-hospital-user {
  --fa: "";
}

.fa-tent-arrow-left-right {
  --fa: "";
}

.fa-gavel, .fa-legal {
  --fa: "";
}

.fa-binoculars {
  --fa: "";
}

.fa-microphone-slash {
  --fa: "";
}

.fa-box-tissue {
  --fa: "";
}

.fa-motorcycle {
  --fa: "";
}

.fa-bell-concierge, .fa-concierge-bell {
  --fa: "";
}

.fa-pen-ruler, .fa-pencil-ruler {
  --fa: "";
}

.fa-people-arrows, .fa-people-arrows-left-right {
  --fa: "";
}

.fa-mars-and-venus-burst {
  --fa: "";
}

.fa-caret-square-right, .fa-square-caret-right {
  --fa: "";
}

.fa-cut, .fa-scissors {
  --fa: "";
}

.fa-sun-plant-wilt {
  --fa: "";
}

.fa-toilets-portable {
  --fa: "";
}

.fa-hockey-puck {
  --fa: "";
}

.fa-table {
  --fa: "";
}

.fa-magnifying-glass-arrow-right {
  --fa: "";
}

.fa-digital-tachograph, .fa-tachograph-digital {
  --fa: "";
}

.fa-users-slash {
  --fa: "";
}

.fa-clover {
  --fa: "";
}

.fa-mail-reply, .fa-reply {
  --fa: "";
}

.fa-star-and-crescent {
  --fa: "";
}

.fa-house-fire {
  --fa: "";
}

.fa-minus-square, .fa-square-minus {
  --fa: "";
}

.fa-helicopter {
  --fa: "";
}

.fa-compass {
  --fa: "";
}

.fa-caret-square-down, .fa-square-caret-down {
  --fa: "";
}

.fa-file-circle-question {
  --fa: "";
}

.fa-laptop-code {
  --fa: "";
}

.fa-swatchbook {
  --fa: "";
}

.fa-prescription-bottle {
  --fa: "";
}

.fa-bars, .fa-navicon {
  --fa: "";
}

.fa-people-group {
  --fa: "";
}

.fa-hourglass-3, .fa-hourglass-end {
  --fa: "";
}

.fa-heart-broken, .fa-heart-crack {
  --fa: "";
}

.fa-external-link-square-alt, .fa-square-up-right {
  --fa: "";
}

.fa-face-kiss-beam, .fa-kiss-beam {
  --fa: "";
}

.fa-film {
  --fa: "";
}

.fa-ruler-horizontal {
  --fa: "";
}

.fa-people-robbery {
  --fa: "";
}

.fa-lightbulb {
  --fa: "";
}

.fa-caret-left {
  --fa: "";
}

.fa-circle-exclamation, .fa-exclamation-circle {
  --fa: "";
}

.fa-school-circle-xmark {
  --fa: "";
}

.fa-arrow-right-from-bracket, .fa-sign-out {
  --fa: "";
}

.fa-chevron-circle-down, .fa-circle-chevron-down {
  --fa: "";
}

.fa-unlock-alt, .fa-unlock-keyhole {
  --fa: "";
}

.fa-cloud-showers-heavy {
  --fa: "";
}

.fa-headphones-alt, .fa-headphones-simple {
  --fa: "";
}

.fa-sitemap {
  --fa: "";
}

.fa-circle-dollar-to-slot, .fa-donate {
  --fa: "";
}

.fa-memory {
  --fa: "";
}

.fa-road-spikes {
  --fa: "";
}

.fa-fire-burner {
  --fa: "";
}

.fa-flag {
  --fa: "";
}

.fa-hanukiah {
  --fa: "";
}

.fa-feather {
  --fa: "";
}

.fa-volume-down, .fa-volume-low {
  --fa: "";
}

.fa-comment-slash {
  --fa: "";
}

.fa-cloud-sun-rain {
  --fa: "";
}

.fa-compress {
  --fa: "";
}

.fa-wheat-alt, .fa-wheat-awn {
  --fa: "";
}

.fa-ankh {
  --fa: "";
}

.fa-hands-holding-child {
  --fa: "";
}

.fa-asterisk {
  --fa: "*";
}

.fa-check-square, .fa-square-check {
  --fa: "";
}

.fa-peseta-sign {
  --fa: "";
}

.fa-header, .fa-heading {
  --fa: "";
}

.fa-ghost {
  --fa: "";
}

.fa-list, .fa-list-squares {
  --fa: "";
}

.fa-phone-square-alt, .fa-square-phone-flip {
  --fa: "";
}

.fa-cart-plus {
  --fa: "";
}

.fa-gamepad {
  --fa: "";
}

.fa-circle-dot, .fa-dot-circle {
  --fa: "";
}

.fa-dizzy, .fa-face-dizzy {
  --fa: "";
}

.fa-egg {
  --fa: "";
}

.fa-house-medical-circle-xmark {
  --fa: "";
}

.fa-campground {
  --fa: "";
}

.fa-folder-plus {
  --fa: "";
}

.fa-futbol, .fa-futbol-ball, .fa-soccer-ball {
  --fa: "";
}

.fa-paint-brush, .fa-paintbrush {
  --fa: "";
}

.fa-lock {
  --fa: "";
}

.fa-gas-pump {
  --fa: "";
}

.fa-hot-tub, .fa-hot-tub-person {
  --fa: "";
}

.fa-map-location, .fa-map-marked {
  --fa: "";
}

.fa-house-flood-water {
  --fa: "";
}

.fa-tree {
  --fa: "";
}

.fa-bridge-lock {
  --fa: "";
}

.fa-sack-dollar {
  --fa: "";
}

.fa-edit, .fa-pen-to-square {
  --fa: "";
}

.fa-car-side {
  --fa: "";
}

.fa-share-alt, .fa-share-nodes {
  --fa: "";
}

.fa-heart-circle-minus {
  --fa: "";
}

.fa-hourglass-2, .fa-hourglass-half {
  --fa: "";
}

.fa-microscope {
  --fa: "";
}

.fa-sink {
  --fa: "";
}

.fa-bag-shopping, .fa-shopping-bag {
  --fa: "";
}

.fa-arrow-down-z-a, .fa-sort-alpha-desc, .fa-sort-alpha-down-alt {
  --fa: "";
}

.fa-mitten {
  --fa: "";
}

.fa-person-rays {
  --fa: "";
}

.fa-users {
  --fa: "";
}

.fa-eye-slash {
  --fa: "";
}

.fa-flask-vial {
  --fa: "";
}

.fa-hand, .fa-hand-paper {
  --fa: "";
}

.fa-om {
  --fa: "";
}

.fa-worm {
  --fa: "";
}

.fa-house-circle-xmark {
  --fa: "";
}

.fa-plug {
  --fa: "";
}

.fa-chevron-up {
  --fa: "";
}

.fa-hand-spock {
  --fa: "";
}

.fa-stopwatch {
  --fa: "";
}

.fa-face-kiss, .fa-kiss {
  --fa: "";
}

.fa-bridge-circle-xmark {
  --fa: "";
}

.fa-face-grin-tongue, .fa-grin-tongue {
  --fa: "";
}

.fa-chess-bishop {
  --fa: "";
}

.fa-face-grin-wink, .fa-grin-wink {
  --fa: "";
}

.fa-deaf, .fa-deafness, .fa-ear-deaf, .fa-hard-of-hearing {
  --fa: "";
}

.fa-road-circle-check {
  --fa: "";
}

.fa-dice-five {
  --fa: "";
}

.fa-rss-square, .fa-square-rss {
  --fa: "";
}

.fa-land-mine-on {
  --fa: "";
}

.fa-i-cursor {
  --fa: "";
}

.fa-stamp {
  --fa: "";
}

.fa-stairs {
  --fa: "";
}

.fa-i {
  --fa: "I";
}

.fa-hryvnia, .fa-hryvnia-sign {
  --fa: "";
}

.fa-pills {
  --fa: "";
}

.fa-face-grin-wide, .fa-grin-alt {
  --fa: "";
}

.fa-tooth {
  --fa: "";
}

.fa-v {
  --fa: "V";
}

.fa-bangladeshi-taka-sign {
  --fa: "";
}

.fa-bicycle {
  --fa: "";
}

.fa-rod-asclepius, .fa-rod-snake, .fa-staff-aesculapius, .fa-staff-snake {
  --fa: "";
}

.fa-head-side-cough-slash {
  --fa: "";
}

.fa-ambulance, .fa-truck-medical {
  --fa: "";
}

.fa-wheat-awn-circle-exclamation {
  --fa: "";
}

.fa-snowman {
  --fa: "";
}

.fa-mortar-pestle {
  --fa: "";
}

.fa-road-barrier {
  --fa: "";
}

.fa-school {
  --fa: "";
}

.fa-igloo {
  --fa: "";
}

.fa-joint {
  --fa: "";
}

.fa-angle-right {
  --fa: "";
}

.fa-horse {
  --fa: "";
}

.fa-q {
  --fa: "Q";
}

.fa-g {
  --fa: "G";
}

.fa-notes-medical {
  --fa: "";
}

.fa-temperature-2, .fa-temperature-half, .fa-thermometer-2, .fa-thermometer-half {
  --fa: "";
}

.fa-dong-sign {
  --fa: "";
}

.fa-capsules {
  --fa: "";
}

.fa-poo-bolt, .fa-poo-storm {
  --fa: "";
}

.fa-face-frown-open, .fa-frown-open {
  --fa: "";
}

.fa-hand-point-up {
  --fa: "";
}

.fa-money-bill {
  --fa: "";
}

.fa-bookmark {
  --fa: "";
}

.fa-align-justify {
  --fa: "";
}

.fa-umbrella-beach {
  --fa: "";
}

.fa-helmet-un {
  --fa: "";
}

.fa-bullseye {
  --fa: "";
}

.fa-bacon {
  --fa: "";
}

.fa-hand-point-down {
  --fa: "";
}

.fa-arrow-up-from-bracket {
  --fa: "";
}

.fa-folder, .fa-folder-blank {
  --fa: "";
}

.fa-file-medical-alt, .fa-file-waveform {
  --fa: "";
}

.fa-radiation {
  --fa: "";
}

.fa-chart-simple {
  --fa: "";
}

.fa-mars-stroke {
  --fa: "";
}

.fa-vial {
  --fa: "";
}

.fa-dashboard, .fa-gauge, .fa-gauge-med, .fa-tachometer-alt-average {
  --fa: "";
}

.fa-magic-wand-sparkles, .fa-wand-magic-sparkles {
  --fa: "";
}

.fa-e {
  --fa: "E";
}

.fa-pen-alt, .fa-pen-clip {
  --fa: "";
}

.fa-bridge-circle-exclamation {
  --fa: "";
}

.fa-user {
  --fa: "";
}

.fa-school-circle-check {
  --fa: "";
}

.fa-dumpster {
  --fa: "";
}

.fa-shuttle-van, .fa-van-shuttle {
  --fa: "";
}

.fa-building-user {
  --fa: "";
}

.fa-caret-square-left, .fa-square-caret-left {
  --fa: "";
}

.fa-highlighter {
  --fa: "";
}

.fa-key {
  --fa: "";
}

.fa-bullhorn {
  --fa: "";
}

.fa-globe {
  --fa: "";
}

.fa-synagogue {
  --fa: "";
}

.fa-person-half-dress {
  --fa: "";
}

.fa-road-bridge {
  --fa: "";
}

.fa-location-arrow {
  --fa: "";
}

.fa-c {
  --fa: "C";
}

.fa-tablet-button {
  --fa: "";
}

.fa-building-lock {
  --fa: "";
}

.fa-pizza-slice {
  --fa: "";
}

.fa-money-bill-wave {
  --fa: "";
}

.fa-area-chart, .fa-chart-area {
  --fa: "";
}

.fa-house-flag {
  --fa: "";
}

.fa-person-circle-minus {
  --fa: "";
}

.fa-ban, .fa-cancel {
  --fa: "";
}

.fa-camera-rotate {
  --fa: "";
}

.fa-air-freshener, .fa-spray-can-sparkles {
  --fa: "";
}

.fa-star {
  --fa: "";
}

.fa-repeat {
  --fa: "";
}

.fa-cross {
  --fa: "";
}

.fa-box {
  --fa: "";
}

.fa-venus-mars {
  --fa: "";
}

.fa-arrow-pointer, .fa-mouse-pointer {
  --fa: "";
}

.fa-expand-arrows-alt, .fa-maximize {
  --fa: "";
}

.fa-charging-station {
  --fa: "";
}

.fa-shapes, .fa-triangle-circle-square {
  --fa: "";
}

.fa-random, .fa-shuffle {
  --fa: "";
}

.fa-person-running, .fa-running {
  --fa: "";
}

.fa-mobile-retro {
  --fa: "";
}

.fa-grip-lines-vertical {
  --fa: "";
}

.fa-spider {
  --fa: "";
}

.fa-hands-bound {
  --fa: "";
}

.fa-file-invoice-dollar {
  --fa: "";
}

.fa-plane-circle-exclamation {
  --fa: "";
}

.fa-x-ray {
  --fa: "";
}

.fa-spell-check {
  --fa: "";
}

.fa-slash {
  --fa: "";
}

.fa-computer-mouse, .fa-mouse {
  --fa: "";
}

.fa-arrow-right-to-bracket, .fa-sign-in {
  --fa: "";
}

.fa-shop-slash, .fa-store-alt-slash {
  --fa: "";
}

.fa-server {
  --fa: "";
}

.fa-virus-covid-slash {
  --fa: "";
}

.fa-shop-lock {
  --fa: "";
}

.fa-hourglass-1, .fa-hourglass-start {
  --fa: "";
}

.fa-blender-phone {
  --fa: "";
}

.fa-building-wheat {
  --fa: "";
}

.fa-person-breastfeeding {
  --fa: "";
}

.fa-right-to-bracket, .fa-sign-in-alt {
  --fa: "";
}

.fa-venus {
  --fa: "";
}

.fa-passport {
  --fa: "";
}

.fa-thumb-tack-slash, .fa-thumbtack-slash {
  --fa: "";
}

.fa-heart-pulse, .fa-heartbeat {
  --fa: "";
}

.fa-people-carry, .fa-people-carry-box {
  --fa: "";
}

.fa-temperature-high {
  --fa: "";
}

.fa-microchip {
  --fa: "";
}

.fa-crown {
  --fa: "";
}

.fa-weight-hanging {
  --fa: "";
}

.fa-xmarks-lines {
  --fa: "";
}

.fa-file-prescription {
  --fa: "";
}

.fa-weight, .fa-weight-scale {
  --fa: "";
}

.fa-user-friends, .fa-user-group {
  --fa: "";
}

.fa-arrow-up-a-z, .fa-sort-alpha-up {
  --fa: "";
}

.fa-chess-knight {
  --fa: "";
}

.fa-face-laugh-squint, .fa-laugh-squint {
  --fa: "";
}

.fa-wheelchair {
  --fa: "";
}

.fa-arrow-circle-up, .fa-circle-arrow-up {
  --fa: "";
}

.fa-toggle-on {
  --fa: "";
}

.fa-person-walking, .fa-walking {
  --fa: "";
}

.fa-l {
  --fa: "L";
}

.fa-fire {
  --fa: "";
}

.fa-bed-pulse, .fa-procedures {
  --fa: "";
}

.fa-shuttle-space, .fa-space-shuttle {
  --fa: "";
}

.fa-face-laugh, .fa-laugh {
  --fa: "";
}

.fa-folder-open {
  --fa: "";
}

.fa-heart-circle-plus {
  --fa: "";
}

.fa-code-fork {
  --fa: "";
}

.fa-city {
  --fa: "";
}

.fa-microphone-alt, .fa-microphone-lines {
  --fa: "";
}

.fa-pepper-hot {
  --fa: "";
}

.fa-unlock {
  --fa: "";
}

.fa-colon-sign {
  --fa: "";
}

.fa-headset {
  --fa: "";
}

.fa-store-slash {
  --fa: "";
}

.fa-road-circle-xmark {
  --fa: "";
}

.fa-user-minus {
  --fa: "";
}

.fa-mars-stroke-up, .fa-mars-stroke-v {
  --fa: "";
}

.fa-champagne-glasses, .fa-glass-cheers {
  --fa: "";
}

.fa-clipboard {
  --fa: "";
}

.fa-house-circle-exclamation {
  --fa: "";
}

.fa-file-arrow-up, .fa-file-upload {
  --fa: "";
}

.fa-wifi, .fa-wifi-3, .fa-wifi-strong {
  --fa: "";
}

.fa-bath, .fa-bathtub {
  --fa: "";
}

.fa-underline {
  --fa: "";
}

.fa-user-edit, .fa-user-pen {
  --fa: "";
}

.fa-signature {
  --fa: "";
}

.fa-stroopwafel {
  --fa: "";
}

.fa-bold {
  --fa: "";
}

.fa-anchor-lock {
  --fa: "";
}

.fa-building-ngo {
  --fa: "";
}

.fa-manat-sign {
  --fa: "";
}

.fa-not-equal {
  --fa: "";
}

.fa-border-style, .fa-border-top-left {
  --fa: "";
}

.fa-map-location-dot, .fa-map-marked-alt {
  --fa: "";
}

.fa-jedi {
  --fa: "";
}

.fa-poll, .fa-square-poll-vertical {
  --fa: "";
}

.fa-mug-hot {
  --fa: "";
}

.fa-battery-car, .fa-car-battery {
  --fa: "";
}

.fa-gift {
  --fa: "";
}

.fa-dice-two {
  --fa: "";
}

.fa-chess-queen {
  --fa: "";
}

.fa-glasses {
  --fa: "";
}

.fa-chess-board {
  --fa: "";
}

.fa-building-circle-check {
  --fa: "";
}

.fa-person-chalkboard {
  --fa: "";
}

.fa-mars-stroke-h, .fa-mars-stroke-right {
  --fa: "";
}

.fa-hand-back-fist, .fa-hand-rock {
  --fa: "";
}

.fa-caret-square-up, .fa-square-caret-up {
  --fa: "";
}

.fa-cloud-showers-water {
  --fa: "";
}

.fa-bar-chart, .fa-chart-bar {
  --fa: "";
}

.fa-hands-bubbles, .fa-hands-wash {
  --fa: "";
}

.fa-less-than-equal {
  --fa: "";
}

.fa-train {
  --fa: "";
}

.fa-eye-low-vision, .fa-low-vision {
  --fa: "";
}

.fa-crow {
  --fa: "";
}

.fa-sailboat {
  --fa: "";
}

.fa-window-restore {
  --fa: "";
}

.fa-plus-square, .fa-square-plus {
  --fa: "";
}

.fa-torii-gate {
  --fa: "";
}

.fa-frog {
  --fa: "";
}

.fa-bucket {
  --fa: "";
}

.fa-image {
  --fa: "";
}

.fa-microphone {
  --fa: "";
}

.fa-cow {
  --fa: "";
}

.fa-caret-up {
  --fa: "";
}

.fa-screwdriver {
  --fa: "";
}

.fa-folder-closed {
  --fa: "";
}

.fa-house-tsunami {
  --fa: "";
}

.fa-square-nfi {
  --fa: "";
}

.fa-arrow-up-from-ground-water {
  --fa: "";
}

.fa-glass-martini-alt, .fa-martini-glass {
  --fa: "";
}

.fa-square-binary {
  --fa: "";
}

.fa-rotate-back, .fa-rotate-backward, .fa-rotate-left, .fa-undo-alt {
  --fa: "";
}

.fa-columns, .fa-table-columns {
  --fa: "";
}

.fa-lemon {
  --fa: "";
}

.fa-head-side-mask {
  --fa: "";
}

.fa-handshake {
  --fa: "";
}

.fa-gem {
  --fa: "";
}

.fa-dolly, .fa-dolly-box {
  --fa: "";
}

.fa-smoking {
  --fa: "";
}

.fa-compress-arrows-alt, .fa-minimize {
  --fa: "";
}

.fa-monument {
  --fa: "";
}

.fa-snowplow {
  --fa: "";
}

.fa-angle-double-right, .fa-angles-right {
  --fa: "";
}

.fa-cannabis {
  --fa: "";
}

.fa-circle-play, .fa-play-circle {
  --fa: "";
}

.fa-tablets {
  --fa: "";
}

.fa-ethernet {
  --fa: "";
}

.fa-eur, .fa-euro, .fa-euro-sign {
  --fa: "";
}

.fa-chair {
  --fa: "";
}

.fa-check-circle, .fa-circle-check {
  --fa: "";
}

.fa-circle-stop, .fa-stop-circle {
  --fa: "";
}

.fa-compass-drafting, .fa-drafting-compass {
  --fa: "";
}

.fa-plate-wheat {
  --fa: "";
}

.fa-icicles {
  --fa: "";
}

.fa-person-shelter {
  --fa: "";
}

.fa-neuter {
  --fa: "";
}

.fa-id-badge {
  --fa: "";
}

.fa-marker {
  --fa: "";
}

.fa-face-laugh-beam, .fa-laugh-beam {
  --fa: "";
}

.fa-helicopter-symbol {
  --fa: "";
}

.fa-universal-access {
  --fa: "";
}

.fa-chevron-circle-up, .fa-circle-chevron-up {
  --fa: "";
}

.fa-lari-sign {
  --fa: "";
}

.fa-volcano {
  --fa: "";
}

.fa-person-walking-dashed-line-arrow-right {
  --fa: "";
}

.fa-gbp, .fa-pound-sign, .fa-sterling-sign {
  --fa: "";
}

.fa-viruses {
  --fa: "";
}

.fa-square-person-confined {
  --fa: "";
}

.fa-user-tie {
  --fa: "";
}

.fa-arrow-down-long, .fa-long-arrow-down {
  --fa: "";
}

.fa-tent-arrow-down-to-line {
  --fa: "";
}

.fa-certificate {
  --fa: "";
}

.fa-mail-reply-all, .fa-reply-all {
  --fa: "";
}

.fa-suitcase {
  --fa: "";
}

.fa-person-skating, .fa-skating {
  --fa: "";
}

.fa-filter-circle-dollar, .fa-funnel-dollar {
  --fa: "";
}

.fa-camera-retro {
  --fa: "";
}

.fa-arrow-circle-down, .fa-circle-arrow-down {
  --fa: "";
}

.fa-arrow-right-to-file, .fa-file-import {
  --fa: "";
}

.fa-external-link-square, .fa-square-arrow-up-right {
  --fa: "";
}

.fa-box-open {
  --fa: "";
}

.fa-scroll {
  --fa: "";
}

.fa-spa {
  --fa: "";
}

.fa-location-pin-lock {
  --fa: "";
}

.fa-pause {
  --fa: "";
}

.fa-hill-avalanche {
  --fa: "";
}

.fa-temperature-0, .fa-temperature-empty, .fa-thermometer-0, .fa-thermometer-empty {
  --fa: "";
}

.fa-bomb {
  --fa: "";
}

.fa-registered {
  --fa: "";
}

.fa-address-card, .fa-contact-card, .fa-vcard {
  --fa: "";
}

.fa-balance-scale-right, .fa-scale-unbalanced-flip {
  --fa: "";
}

.fa-subscript {
  --fa: "";
}

.fa-diamond-turn-right, .fa-directions {
  --fa: "";
}

.fa-burst {
  --fa: "";
}

.fa-house-laptop, .fa-laptop-house {
  --fa: "";
}

.fa-face-tired, .fa-tired {
  --fa: "";
}

.fa-money-bills {
  --fa: "";
}

.fa-smog {
  --fa: "";
}

.fa-crutch {
  --fa: "";
}

.fa-cloud-arrow-up, .fa-cloud-upload, .fa-cloud-upload-alt {
  --fa: "";
}

.fa-palette {
  --fa: "";
}

.fa-arrows-turn-right {
  --fa: "";
}

.fa-vest {
  --fa: "";
}

.fa-ferry {
  --fa: "";
}

.fa-arrows-down-to-people {
  --fa: "";
}

.fa-seedling, .fa-sprout {
  --fa: "";
}

.fa-arrows-alt-h, .fa-left-right {
  --fa: "";
}

.fa-boxes-packing {
  --fa: "";
}

.fa-arrow-circle-left, .fa-circle-arrow-left {
  --fa: "";
}

.fa-group-arrows-rotate {
  --fa: "";
}

.fa-bowl-food {
  --fa: "";
}

.fa-candy-cane {
  --fa: "";
}

.fa-arrow-down-wide-short, .fa-sort-amount-asc, .fa-sort-amount-down {
  --fa: "";
}

.fa-cloud-bolt, .fa-thunderstorm {
  --fa: "";
}

.fa-remove-format, .fa-text-slash {
  --fa: "";
}

.fa-face-smile-wink, .fa-smile-wink {
  --fa: "";
}

.fa-file-word {
  --fa: "";
}

.fa-file-powerpoint {
  --fa: "";
}

.fa-arrows-h, .fa-arrows-left-right {
  --fa: "";
}

.fa-house-lock {
  --fa: "";
}

.fa-cloud-arrow-down, .fa-cloud-download, .fa-cloud-download-alt {
  --fa: "";
}

.fa-children {
  --fa: "";
}

.fa-blackboard, .fa-chalkboard {
  --fa: "";
}

.fa-user-alt-slash, .fa-user-large-slash {
  --fa: "";
}

.fa-envelope-open {
  --fa: "";
}

.fa-handshake-alt-slash, .fa-handshake-simple-slash {
  --fa: "";
}

.fa-mattress-pillow {
  --fa: "";
}

.fa-guarani-sign {
  --fa: "";
}

.fa-arrows-rotate, .fa-refresh, .fa-sync {
  --fa: "";
}

.fa-fire-extinguisher {
  --fa: "";
}

.fa-cruzeiro-sign {
  --fa: "";
}

.fa-greater-than-equal {
  --fa: "";
}

.fa-shield-alt, .fa-shield-halved {
  --fa: "";
}

.fa-atlas, .fa-book-atlas {
  --fa: "";
}

.fa-virus {
  --fa: "";
}

.fa-envelope-circle-check {
  --fa: "";
}

.fa-layer-group {
  --fa: "";
}

.fa-arrows-to-dot {
  --fa: "";
}

.fa-archway {
  --fa: "";
}

.fa-heart-circle-check {
  --fa: "";
}

.fa-house-chimney-crack, .fa-house-damage {
  --fa: "";
}

.fa-file-archive, .fa-file-zipper {
  --fa: "";
}

.fa-square {
  --fa: "";
}

.fa-glass-martini, .fa-martini-glass-empty {
  --fa: "";
}

.fa-couch {
  --fa: "";
}

.fa-cedi-sign {
  --fa: "";
}

.fa-italic {
  --fa: "";
}

.fa-table-cells-column-lock {
  --fa: "";
}

.fa-church {
  --fa: "";
}

.fa-comments-dollar {
  --fa: "";
}

.fa-democrat {
  --fa: "";
}

.fa-z {
  --fa: "Z";
}

.fa-person-skiing, .fa-skiing {
  --fa: "";
}

.fa-road-lock {
  --fa: "";
}

.fa-a {
  --fa: "A";
}

.fa-temperature-arrow-down, .fa-temperature-down {
  --fa: "";
}

.fa-feather-alt, .fa-feather-pointed {
  --fa: "";
}

.fa-p {
  --fa: "P";
}

.fa-snowflake {
  --fa: "";
}

.fa-newspaper {
  --fa: "";
}

.fa-ad, .fa-rectangle-ad {
  --fa: "";
}

.fa-arrow-circle-right, .fa-circle-arrow-right {
  --fa: "";
}

.fa-filter-circle-xmark {
  --fa: "";
}

.fa-locust {
  --fa: "";
}

.fa-sort, .fa-unsorted {
  --fa: "";
}

.fa-list-1-2, .fa-list-numeric, .fa-list-ol {
  --fa: "";
}

.fa-person-dress-burst {
  --fa: "";
}

.fa-money-check-alt, .fa-money-check-dollar {
  --fa: "";
}

.fa-vector-square {
  --fa: "";
}

.fa-bread-slice {
  --fa: "";
}

.fa-language {
  --fa: "";
}

.fa-face-kiss-wink-heart, .fa-kiss-wink-heart {
  --fa: "";
}

.fa-filter {
  --fa: "";
}

.fa-question {
  --fa: "?";
}

.fa-file-signature {
  --fa: "";
}

.fa-arrows-alt, .fa-up-down-left-right {
  --fa: "";
}

.fa-house-chimney-user {
  --fa: "";
}

.fa-hand-holding-heart {
  --fa: "";
}

.fa-puzzle-piece {
  --fa: "";
}

.fa-money-check {
  --fa: "";
}

.fa-star-half-alt, .fa-star-half-stroke {
  --fa: "";
}

.fa-code {
  --fa: "";
}

.fa-glass-whiskey, .fa-whiskey-glass {
  --fa: "";
}

.fa-building-circle-exclamation {
  --fa: "";
}

.fa-magnifying-glass-chart {
  --fa: "";
}

.fa-arrow-up-right-from-square, .fa-external-link {
  --fa: "";
}

.fa-cubes-stacked {
  --fa: "";
}

.fa-krw, .fa-won, .fa-won-sign {
  --fa: "";
}

.fa-virus-covid {
  --fa: "";
}

.fa-austral-sign {
  --fa: "";
}

.fa-f {
  --fa: "F";
}

.fa-leaf {
  --fa: "";
}

.fa-road {
  --fa: "";
}

.fa-cab, .fa-taxi {
  --fa: "";
}

.fa-person-circle-plus {
  --fa: "";
}

.fa-chart-pie, .fa-pie-chart {
  --fa: "";
}

.fa-bolt-lightning {
  --fa: "";
}

.fa-sack-xmark {
  --fa: "";
}

.fa-file-excel {
  --fa: "";
}

.fa-file-contract {
  --fa: "";
}

.fa-fish-fins {
  --fa: "";
}

.fa-building-flag {
  --fa: "";
}

.fa-face-grin-beam, .fa-grin-beam {
  --fa: "";
}

.fa-object-ungroup {
  --fa: "";
}

.fa-poop {
  --fa: "";
}

.fa-location-pin, .fa-map-marker {
  --fa: "";
}

.fa-kaaba {
  --fa: "";
}

.fa-toilet-paper {
  --fa: "";
}

.fa-hard-hat, .fa-hat-hard, .fa-helmet-safety {
  --fa: "";
}

.fa-eject {
  --fa: "";
}

.fa-arrow-alt-circle-right, .fa-circle-right {
  --fa: "";
}

.fa-plane-circle-check {
  --fa: "";
}

.fa-face-rolling-eyes, .fa-meh-rolling-eyes {
  --fa: "";
}

.fa-object-group {
  --fa: "";
}

.fa-chart-line, .fa-line-chart {
  --fa: "";
}

.fa-mask-ventilator {
  --fa: "";
}

.fa-arrow-right {
  --fa: "";
}

.fa-map-signs, .fa-signs-post {
  --fa: "";
}

.fa-cash-register {
  --fa: "";
}

.fa-person-circle-question {
  --fa: "";
}

.fa-h {
  --fa: "H";
}

.fa-tarp {
  --fa: "";
}

.fa-screwdriver-wrench, .fa-tools {
  --fa: "";
}

.fa-arrows-to-eye {
  --fa: "";
}

.fa-plug-circle-bolt {
  --fa: "";
}

.fa-heart {
  --fa: "";
}

.fa-mars-and-venus {
  --fa: "";
}

.fa-home-user, .fa-house-user {
  --fa: "";
}

.fa-dumpster-fire {
  --fa: "";
}

.fa-house-crack {
  --fa: "";
}

.fa-cocktail, .fa-martini-glass-citrus {
  --fa: "";
}

.fa-face-surprise, .fa-surprise {
  --fa: "";
}

.fa-bottle-water {
  --fa: "";
}

.fa-circle-pause, .fa-pause-circle {
  --fa: "";
}

.fa-toilet-paper-slash {
  --fa: "";
}

.fa-apple-alt, .fa-apple-whole {
  --fa: "";
}

.fa-kitchen-set {
  --fa: "";
}

.fa-r {
  --fa: "R";
}

.fa-temperature-1, .fa-temperature-quarter, .fa-thermometer-1, .fa-thermometer-quarter {
  --fa: "";
}

.fa-cube {
  --fa: "";
}

.fa-bitcoin-sign {
  --fa: "";
}

.fa-shield-dog {
  --fa: "";
}

.fa-solar-panel {
  --fa: "";
}

.fa-lock-open {
  --fa: "";
}

.fa-elevator {
  --fa: "";
}

.fa-money-bill-transfer {
  --fa: "";
}

.fa-money-bill-trend-up {
  --fa: "";
}

.fa-house-flood-water-circle-arrow-right {
  --fa: "";
}

.fa-poll-h, .fa-square-poll-horizontal {
  --fa: "";
}

.fa-circle {
  --fa: "";
}

.fa-backward-fast, .fa-fast-backward {
  --fa: "";
}

.fa-recycle {
  --fa: "";
}

.fa-user-astronaut {
  --fa: "";
}

.fa-plane-slash {
  --fa: "";
}

.fa-trademark {
  --fa: "";
}

.fa-basketball, .fa-basketball-ball {
  --fa: "";
}

.fa-satellite-dish {
  --fa: "";
}

.fa-arrow-alt-circle-up, .fa-circle-up {
  --fa: "";
}

.fa-mobile-alt, .fa-mobile-screen-button {
  --fa: "";
}

.fa-volume-high, .fa-volume-up {
  --fa: "";
}

.fa-users-rays {
  --fa: "";
}

.fa-wallet {
  --fa: "";
}

.fa-clipboard-check {
  --fa: "";
}

.fa-file-audio {
  --fa: "";
}

.fa-burger, .fa-hamburger {
  --fa: "";
}

.fa-wrench {
  --fa: "";
}

.fa-bugs {
  --fa: "";
}

.fa-rupee, .fa-rupee-sign {
  --fa: "";
}

.fa-file-image {
  --fa: "";
}

.fa-circle-question, .fa-question-circle {
  --fa: "";
}

.fa-plane-departure {
  --fa: "";
}

.fa-handshake-slash {
  --fa: "";
}

.fa-book-bookmark {
  --fa: "";
}

.fa-code-branch {
  --fa: "";
}

.fa-hat-cowboy {
  --fa: "";
}

.fa-bridge {
  --fa: "";
}

.fa-phone-alt, .fa-phone-flip {
  --fa: "";
}

.fa-truck-front {
  --fa: "";
}

.fa-cat {
  --fa: "";
}

.fa-anchor-circle-exclamation {
  --fa: "";
}

.fa-truck-field {
  --fa: "";
}

.fa-route {
  --fa: "";
}

.fa-clipboard-question {
  --fa: "";
}

.fa-panorama {
  --fa: "";
}

.fa-comment-medical {
  --fa: "";
}

.fa-teeth-open {
  --fa: "";
}

.fa-file-circle-minus {
  --fa: "";
}

.fa-tags {
  --fa: "";
}

.fa-wine-glass {
  --fa: "";
}

.fa-fast-forward, .fa-forward-fast {
  --fa: "";
}

.fa-face-meh-blank, .fa-meh-blank {
  --fa: "";
}

.fa-parking, .fa-square-parking {
  --fa: "";
}

.fa-house-signal {
  --fa: "";
}

.fa-bars-progress, .fa-tasks-alt {
  --fa: "";
}

.fa-faucet-drip {
  --fa: "";
}

.fa-cart-flatbed, .fa-dolly-flatbed {
  --fa: "";
}

.fa-ban-smoking, .fa-smoking-ban {
  --fa: "";
}

.fa-terminal {
  --fa: "";
}

.fa-mobile-button {
  --fa: "";
}

.fa-house-medical-flag {
  --fa: "";
}

.fa-basket-shopping, .fa-shopping-basket {
  --fa: "";
}

.fa-tape {
  --fa: "";
}

.fa-bus-alt, .fa-bus-simple {
  --fa: "";
}

.fa-eye {
  --fa: "";
}

.fa-face-sad-cry, .fa-sad-cry {
  --fa: "";
}

.fa-audio-description {
  --fa: "";
}

.fa-person-military-to-person {
  --fa: "";
}

.fa-file-shield {
  --fa: "";
}

.fa-user-slash {
  --fa: "";
}

.fa-pen {
  --fa: "";
}

.fa-tower-observation {
  --fa: "";
}

.fa-file-code {
  --fa: "";
}

.fa-signal, .fa-signal-5, .fa-signal-perfect {
  --fa: "";
}

.fa-bus {
  --fa: "";
}

.fa-heart-circle-xmark {
  --fa: "";
}

.fa-home-lg, .fa-house-chimney {
  --fa: "";
}

.fa-window-maximize {
  --fa: "";
}

.fa-face-frown, .fa-frown {
  --fa: "";
}

.fa-prescription {
  --fa: "";
}

.fa-shop, .fa-store-alt {
  --fa: "";
}

.fa-floppy-disk, .fa-save {
  --fa: "";
}

.fa-vihara {
  --fa: "";
}

.fa-balance-scale-left, .fa-scale-unbalanced {
  --fa: "";
}

.fa-sort-asc, .fa-sort-up {
  --fa: "";
}

.fa-comment-dots, .fa-commenting {
  --fa: "";
}

.fa-plant-wilt {
  --fa: "";
}

.fa-diamond {
  --fa: "";
}

.fa-face-grin-squint, .fa-grin-squint {
  --fa: "";
}

.fa-hand-holding-dollar, .fa-hand-holding-usd {
  --fa: "";
}

.fa-chart-diagram {
  --fa: "";
}

.fa-bacterium {
  --fa: "";
}

.fa-hand-pointer {
  --fa: "";
}

.fa-drum-steelpan {
  --fa: "";
}

.fa-hand-scissors {
  --fa: "";
}

.fa-hands-praying, .fa-praying-hands {
  --fa: "";
}

.fa-arrow-right-rotate, .fa-arrow-rotate-forward, .fa-arrow-rotate-right, .fa-redo {
  --fa: "";
}

.fa-biohazard {
  --fa: "";
}

.fa-location, .fa-location-crosshairs {
  --fa: "";
}

.fa-mars-double {
  --fa: "";
}

.fa-child-dress {
  --fa: "";
}

.fa-users-between-lines {
  --fa: "";
}

.fa-lungs-virus {
  --fa: "";
}

.fa-face-grin-tears, .fa-grin-tears {
  --fa: "";
}

.fa-phone {
  --fa: "";
}

.fa-calendar-times, .fa-calendar-xmark {
  --fa: "";
}

.fa-child-reaching {
  --fa: "";
}

.fa-head-side-virus {
  --fa: "";
}

.fa-user-cog, .fa-user-gear {
  --fa: "";
}

.fa-arrow-up-1-9, .fa-sort-numeric-up {
  --fa: "";
}

.fa-door-closed {
  --fa: "";
}

.fa-shield-virus {
  --fa: "";
}

.fa-dice-six {
  --fa: "";
}

.fa-mosquito-net {
  --fa: "";
}

.fa-file-fragment {
  --fa: "";
}

.fa-bridge-water {
  --fa: "";
}

.fa-person-booth {
  --fa: "";
}

.fa-text-width {
  --fa: "";
}

.fa-hat-wizard {
  --fa: "";
}

.fa-pen-fancy {
  --fa: "";
}

.fa-digging, .fa-person-digging {
  --fa: "";
}

.fa-trash {
  --fa: "";
}

.fa-gauge-simple, .fa-gauge-simple-med, .fa-tachometer-average {
  --fa: "";
}

.fa-book-medical {
  --fa: "";
}

.fa-poo {
  --fa: "";
}

.fa-quote-right, .fa-quote-right-alt {
  --fa: "";
}

.fa-shirt, .fa-t-shirt, .fa-tshirt {
  --fa: "";
}

.fa-cubes {
  --fa: "";
}

.fa-divide {
  --fa: "";
}

.fa-tenge, .fa-tenge-sign {
  --fa: "";
}

.fa-headphones {
  --fa: "";
}

.fa-hands-holding {
  --fa: "";
}

.fa-hands-clapping {
  --fa: "";
}

.fa-republican {
  --fa: "";
}

.fa-arrow-left {
  --fa: "";
}

.fa-person-circle-xmark {
  --fa: "";
}

.fa-ruler {
  --fa: "";
}

.fa-align-left {
  --fa: "";
}

.fa-dice-d6 {
  --fa: "";
}

.fa-restroom {
  --fa: "";
}

.fa-j {
  --fa: "J";
}

.fa-users-viewfinder {
  --fa: "";
}

.fa-file-video {
  --fa: "";
}

.fa-external-link-alt, .fa-up-right-from-square {
  --fa: "";
}

.fa-table-cells, .fa-th {
  --fa: "";
}

.fa-file-pdf {
  --fa: "";
}

.fa-bible, .fa-book-bible {
  --fa: "";
}

.fa-o {
  --fa: "O";
}

.fa-medkit, .fa-suitcase-medical {
  --fa: "";
}

.fa-user-secret {
  --fa: "";
}

.fa-otter {
  --fa: "";
}

.fa-female, .fa-person-dress {
  --fa: "";
}

.fa-comment-dollar {
  --fa: "";
}

.fa-briefcase-clock, .fa-business-time {
  --fa: "";
}

.fa-table-cells-large, .fa-th-large {
  --fa: "";
}

.fa-book-tanakh, .fa-tanakh {
  --fa: "";
}

.fa-phone-volume, .fa-volume-control-phone {
  --fa: "";
}

.fa-hat-cowboy-side {
  --fa: "";
}

.fa-clipboard-user {
  --fa: "";
}

.fa-child {
  --fa: "";
}

.fa-lira-sign {
  --fa: "";
}

.fa-satellite {
  --fa: "";
}

.fa-plane-lock {
  --fa: "";
}

.fa-tag {
  --fa: "";
}

.fa-comment {
  --fa: "";
}

.fa-birthday-cake, .fa-cake, .fa-cake-candles {
  --fa: "";
}

.fa-envelope {
  --fa: "";
}

.fa-angle-double-up, .fa-angles-up {
  --fa: "";
}

.fa-paperclip {
  --fa: "";
}

.fa-arrow-right-to-city {
  --fa: "";
}

.fa-ribbon {
  --fa: "";
}

.fa-lungs {
  --fa: "";
}

.fa-arrow-up-9-1, .fa-sort-numeric-up-alt {
  --fa: "";
}

.fa-litecoin-sign {
  --fa: "";
}

.fa-border-none {
  --fa: "";
}

.fa-circle-nodes {
  --fa: "";
}

.fa-parachute-box {
  --fa: "";
}

.fa-indent {
  --fa: "";
}

.fa-truck-field-un {
  --fa: "";
}

.fa-hourglass, .fa-hourglass-empty {
  --fa: "";
}

.fa-mountain {
  --fa: "";
}

.fa-user-doctor, .fa-user-md {
  --fa: "";
}

.fa-circle-info, .fa-info-circle {
  --fa: "";
}

.fa-cloud-meatball {
  --fa: "";
}

.fa-camera, .fa-camera-alt {
  --fa: "";
}

.fa-square-virus {
  --fa: "";
}

.fa-meteor {
  --fa: "";
}

.fa-car-on {
  --fa: "";
}

.fa-sleigh {
  --fa: "";
}

.fa-arrow-down-1-9, .fa-sort-numeric-asc, .fa-sort-numeric-down {
  --fa: "";
}

.fa-hand-holding-droplet, .fa-hand-holding-water {
  --fa: "";
}

.fa-water {
  --fa: "";
}

.fa-calendar-check {
  --fa: "";
}

.fa-braille {
  --fa: "";
}

.fa-prescription-bottle-alt, .fa-prescription-bottle-medical {
  --fa: "";
}

.fa-landmark {
  --fa: "";
}

.fa-truck {
  --fa: "";
}

.fa-crosshairs {
  --fa: "";
}

.fa-person-cane {
  --fa: "";
}

.fa-tent {
  --fa: "";
}

.fa-vest-patches {
  --fa: "";
}

.fa-check-double {
  --fa: "";
}

.fa-arrow-down-a-z, .fa-sort-alpha-asc, .fa-sort-alpha-down {
  --fa: "";
}

.fa-money-bill-wheat {
  --fa: "";
}

.fa-cookie {
  --fa: "";
}

.fa-arrow-left-rotate, .fa-arrow-rotate-back, .fa-arrow-rotate-backward, .fa-arrow-rotate-left, .fa-undo {
  --fa: "";
}

.fa-hard-drive, .fa-hdd {
  --fa: "";
}

.fa-face-grin-squint-tears, .fa-grin-squint-tears {
  --fa: "";
}

.fa-dumbbell {
  --fa: "";
}

.fa-list-alt, .fa-rectangle-list {
  --fa: "";
}

.fa-tarp-droplet {
  --fa: "";
}

.fa-house-medical-circle-check {
  --fa: "";
}

.fa-person-skiing-nordic, .fa-skiing-nordic {
  --fa: "";
}

.fa-calendar-plus {
  --fa: "";
}

.fa-plane-arrival {
  --fa: "";
}

.fa-arrow-alt-circle-left, .fa-circle-left {
  --fa: "";
}

.fa-subway, .fa-train-subway {
  --fa: "";
}

.fa-chart-gantt {
  --fa: "";
}

.fa-indian-rupee, .fa-indian-rupee-sign, .fa-inr {
  --fa: "";
}

.fa-crop-alt, .fa-crop-simple {
  --fa: "";
}

.fa-money-bill-1, .fa-money-bill-alt {
  --fa: "";
}

.fa-left-long, .fa-long-arrow-alt-left {
  --fa: "";
}

.fa-dna {
  --fa: "";
}

.fa-virus-slash {
  --fa: "";
}

.fa-minus, .fa-subtract {
  --fa: "";
}

.fa-chess {
  --fa: "";
}

.fa-arrow-left-long, .fa-long-arrow-left {
  --fa: "";
}

.fa-plug-circle-check {
  --fa: "";
}

.fa-street-view {
  --fa: "";
}

.fa-franc-sign {
  --fa: "";
}

.fa-volume-off {
  --fa: "";
}

.fa-american-sign-language-interpreting, .fa-asl-interpreting, .fa-hands-american-sign-language-interpreting, .fa-hands-asl-interpreting {
  --fa: "";
}

.fa-cog, .fa-gear {
  --fa: "";
}

.fa-droplet-slash, .fa-tint-slash {
  --fa: "";
}

.fa-mosque {
  --fa: "";
}

.fa-mosquito {
  --fa: "";
}

.fa-star-of-david {
  --fa: "";
}

.fa-person-military-rifle {
  --fa: "";
}

.fa-cart-shopping, .fa-shopping-cart {
  --fa: "";
}

.fa-vials {
  --fa: "";
}

.fa-plug-circle-plus {
  --fa: "";
}

.fa-place-of-worship {
  --fa: "";
}

.fa-grip-vertical {
  --fa: "";
}

.fa-hexagon-nodes {
  --fa: "";
}

.fa-arrow-turn-up, .fa-level-up {
  --fa: "";
}

.fa-u {
  --fa: "U";
}

.fa-square-root-alt, .fa-square-root-variable {
  --fa: "";
}

.fa-clock, .fa-clock-four {
  --fa: "";
}

.fa-backward-step, .fa-step-backward {
  --fa: "";
}

.fa-pallet {
  --fa: "";
}

.fa-faucet {
  --fa: "";
}

.fa-baseball-bat-ball {
  --fa: "";
}

.fa-s {
  --fa: "S";
}

.fa-timeline {
  --fa: "";
}

.fa-keyboard {
  --fa: "";
}

.fa-caret-down {
  --fa: "";
}

.fa-clinic-medical, .fa-house-chimney-medical {
  --fa: "";
}

.fa-temperature-3, .fa-temperature-three-quarters, .fa-thermometer-3, .fa-thermometer-three-quarters {
  --fa: "";
}

.fa-mobile-android-alt, .fa-mobile-screen {
  --fa: "";
}

.fa-plane-up {
  --fa: "";
}

.fa-piggy-bank {
  --fa: "";
}

.fa-battery-3, .fa-battery-half {
  --fa: "";
}

.fa-mountain-city {
  --fa: "";
}

.fa-coins {
  --fa: "";
}

.fa-khanda {
  --fa: "";
}

.fa-sliders, .fa-sliders-h {
  --fa: "";
}

.fa-folder-tree {
  --fa: "";
}

.fa-network-wired {
  --fa: "";
}

.fa-map-pin {
  --fa: "";
}

.fa-hamsa {
  --fa: "";
}

.fa-cent-sign {
  --fa: "";
}

.fa-flask {
  --fa: "";
}

.fa-person-pregnant {
  --fa: "";
}

.fa-wand-sparkles {
  --fa: "";
}

.fa-ellipsis-v, .fa-ellipsis-vertical {
  --fa: "";
}

.fa-ticket {
  --fa: "";
}

.fa-power-off {
  --fa: "";
}

.fa-long-arrow-alt-right, .fa-right-long {
  --fa: "";
}

.fa-flag-usa {
  --fa: "";
}

.fa-laptop-file {
  --fa: "";
}

.fa-teletype, .fa-tty {
  --fa: "";
}

.fa-diagram-next {
  --fa: "";
}

.fa-person-rifle {
  --fa: "";
}

.fa-house-medical-circle-exclamation {
  --fa: "";
}

.fa-closed-captioning {
  --fa: "";
}

.fa-hiking, .fa-person-hiking {
  --fa: "";
}

.fa-venus-double {
  --fa: "";
}

.fa-images {
  --fa: "";
}

.fa-calculator {
  --fa: "";
}

.fa-people-pulling {
  --fa: "";
}

.fa-n {
  --fa: "N";
}

.fa-cable-car, .fa-tram {
  --fa: "";
}

.fa-cloud-rain {
  --fa: "";
}

.fa-building-circle-xmark {
  --fa: "";
}

.fa-ship {
  --fa: "";
}

.fa-arrows-down-to-line {
  --fa: "";
}

.fa-download {
  --fa: "";
}

.fa-face-grin, .fa-grin {
  --fa: "";
}

.fa-backspace, .fa-delete-left {
  --fa: "";
}

.fa-eye-dropper, .fa-eye-dropper-empty, .fa-eyedropper {
  --fa: "";
}

.fa-file-circle-check {
  --fa: "";
}

.fa-forward {
  --fa: "";
}

.fa-mobile, .fa-mobile-android, .fa-mobile-phone {
  --fa: "";
}

.fa-face-meh, .fa-meh {
  --fa: "";
}

.fa-align-center {
  --fa: "";
}

.fa-book-dead, .fa-book-skull {
  --fa: "";
}

.fa-drivers-license, .fa-id-card {
  --fa: "";
}

.fa-dedent, .fa-outdent {
  --fa: "";
}

.fa-heart-circle-exclamation {
  --fa: "";
}

.fa-home, .fa-home-alt, .fa-home-lg-alt, .fa-house {
  --fa: "";
}

.fa-calendar-week {
  --fa: "";
}

.fa-laptop-medical {
  --fa: "";
}

.fa-b {
  --fa: "B";
}

.fa-file-medical {
  --fa: "";
}

.fa-dice-one {
  --fa: "";
}

.fa-kiwi-bird {
  --fa: "";
}

.fa-arrow-right-arrow-left, .fa-exchange {
  --fa: "";
}

.fa-redo-alt, .fa-rotate-forward, .fa-rotate-right {
  --fa: "";
}

.fa-cutlery, .fa-utensils {
  --fa: "";
}

.fa-arrow-up-wide-short, .fa-sort-amount-up {
  --fa: "";
}

.fa-mill-sign {
  --fa: "";
}

.fa-bowl-rice {
  --fa: "";
}

.fa-skull {
  --fa: "";
}

.fa-broadcast-tower, .fa-tower-broadcast {
  --fa: "";
}

.fa-truck-pickup {
  --fa: "";
}

.fa-long-arrow-alt-up, .fa-up-long {
  --fa: "";
}

.fa-stop {
  --fa: "";
}

.fa-code-merge {
  --fa: "";
}

.fa-upload {
  --fa: "";
}

.fa-hurricane {
  --fa: "";
}

.fa-mound {
  --fa: "";
}

.fa-toilet-portable {
  --fa: "";
}

.fa-compact-disc {
  --fa: "";
}

.fa-file-arrow-down, .fa-file-download {
  --fa: "";
}

.fa-caravan {
  --fa: "";
}

.fa-shield-cat {
  --fa: "";
}

.fa-bolt, .fa-zap {
  --fa: "";
}

.fa-glass-water {
  --fa: "";
}

.fa-oil-well {
  --fa: "";
}

.fa-vault {
  --fa: "";
}

.fa-mars {
  --fa: "";
}

.fa-toilet {
  --fa: "";
}

.fa-plane-circle-xmark {
  --fa: "";
}

.fa-cny, .fa-jpy, .fa-rmb, .fa-yen, .fa-yen-sign {
  --fa: "";
}

.fa-rouble, .fa-rub, .fa-ruble, .fa-ruble-sign {
  --fa: "";
}

.fa-sun {
  --fa: "";
}

.fa-guitar {
  --fa: "";
}

.fa-face-laugh-wink, .fa-laugh-wink {
  --fa: "";
}

.fa-horse-head {
  --fa: "";
}

.fa-bore-hole {
  --fa: "";
}

.fa-industry {
  --fa: "";
}

.fa-arrow-alt-circle-down, .fa-circle-down {
  --fa: "";
}

.fa-arrows-turn-to-dots {
  --fa: "";
}

.fa-florin-sign {
  --fa: "";
}

.fa-arrow-down-short-wide, .fa-sort-amount-desc, .fa-sort-amount-down-alt {
  --fa: "";
}

.fa-less-than {
  --fa: "<";
}

.fa-angle-down {
  --fa: "";
}

.fa-car-tunnel {
  --fa: "";
}

.fa-head-side-cough {
  --fa: "";
}

.fa-grip-lines {
  --fa: "";
}

.fa-thumbs-down {
  --fa: "";
}

.fa-user-lock {
  --fa: "";
}

.fa-arrow-right-long, .fa-long-arrow-right {
  --fa: "";
}

.fa-anchor-circle-xmark {
  --fa: "";
}

.fa-ellipsis, .fa-ellipsis-h {
  --fa: "";
}

.fa-chess-pawn {
  --fa: "";
}

.fa-first-aid, .fa-kit-medical {
  --fa: "";
}

.fa-person-through-window {
  --fa: "";
}

.fa-toolbox {
  --fa: "";
}

.fa-hands-holding-circle {
  --fa: "";
}

.fa-bug {
  --fa: "";
}

.fa-credit-card, .fa-credit-card-alt {
  --fa: "";
}

.fa-automobile, .fa-car {
  --fa: "";
}

.fa-hand-holding-hand {
  --fa: "";
}

.fa-book-open-reader, .fa-book-reader {
  --fa: "";
}

.fa-mountain-sun {
  --fa: "";
}

.fa-arrows-left-right-to-line {
  --fa: "";
}

.fa-dice-d20 {
  --fa: "";
}

.fa-truck-droplet {
  --fa: "";
}

.fa-file-circle-xmark {
  --fa: "";
}

.fa-temperature-arrow-up, .fa-temperature-up {
  --fa: "";
}

.fa-medal {
  --fa: "";
}

.fa-bed {
  --fa: "";
}

.fa-h-square, .fa-square-h {
  --fa: "";
}

.fa-podcast {
  --fa: "";
}

.fa-temperature-4, .fa-temperature-full, .fa-thermometer-4, .fa-thermometer-full {
  --fa: "";
}

.fa-bell {
  --fa: "";
}

.fa-superscript {
  --fa: "";
}

.fa-plug-circle-xmark {
  --fa: "";
}

.fa-star-of-life {
  --fa: "";
}

.fa-phone-slash {
  --fa: "";
}

.fa-paint-roller {
  --fa: "";
}

.fa-hands-helping, .fa-handshake-angle {
  --fa: "";
}

.fa-location-dot, .fa-map-marker-alt {
  --fa: "";
}

.fa-file {
  --fa: "";
}

.fa-greater-than {
  --fa: ">";
}

.fa-person-swimming, .fa-swimmer {
  --fa: "";
}

.fa-arrow-down {
  --fa: "";
}

.fa-droplet, .fa-tint {
  --fa: "";
}

.fa-eraser {
  --fa: "";
}

.fa-earth, .fa-earth-america, .fa-earth-americas, .fa-globe-americas {
  --fa: "";
}

.fa-person-burst {
  --fa: "";
}

.fa-dove {
  --fa: "";
}

.fa-battery-0, .fa-battery-empty {
  --fa: "";
}

.fa-socks {
  --fa: "";
}

.fa-inbox {
  --fa: "";
}

.fa-section {
  --fa: "";
}

.fa-gauge-high, .fa-tachometer-alt, .fa-tachometer-alt-fast {
  --fa: "";
}

.fa-envelope-open-text {
  --fa: "";
}

.fa-hospital, .fa-hospital-alt, .fa-hospital-wide {
  --fa: "";
}

.fa-wine-bottle {
  --fa: "";
}

.fa-chess-rook {
  --fa: "";
}

.fa-bars-staggered, .fa-reorder, .fa-stream {
  --fa: "";
}

.fa-dharmachakra {
  --fa: "";
}

.fa-hotdog {
  --fa: "";
}

.fa-blind, .fa-person-walking-with-cane {
  --fa: "";
}

.fa-drum {
  --fa: "";
}

.fa-ice-cream {
  --fa: "";
}

.fa-heart-circle-bolt {
  --fa: "";
}

.fa-fax {
  --fa: "";
}

.fa-paragraph {
  --fa: "";
}

.fa-check-to-slot, .fa-vote-yea {
  --fa: "";
}

.fa-star-half {
  --fa: "";
}

.fa-boxes, .fa-boxes-alt, .fa-boxes-stacked {
  --fa: "";
}

.fa-chain, .fa-link {
  --fa: "";
}

.fa-assistive-listening-systems, .fa-ear-listen {
  --fa: "";
}

.fa-tree-city {
  --fa: "";
}

.fa-play {
  --fa: "";
}

.fa-font {
  --fa: "";
}

.fa-table-cells-row-lock {
  --fa: "";
}

.fa-rupiah-sign {
  --fa: "";
}

.fa-magnifying-glass, .fa-search {
  --fa: "";
}

.fa-ping-pong-paddle-ball, .fa-table-tennis, .fa-table-tennis-paddle-ball {
  --fa: "";
}

.fa-diagnoses, .fa-person-dots-from-line {
  --fa: "";
}

.fa-trash-can-arrow-up, .fa-trash-restore-alt {
  --fa: "";
}

.fa-naira-sign {
  --fa: "";
}

.fa-cart-arrow-down {
  --fa: "";
}

.fa-walkie-talkie {
  --fa: "";
}

.fa-file-edit, .fa-file-pen {
  --fa: "";
}

.fa-receipt {
  --fa: "";
}

.fa-pen-square, .fa-pencil-square, .fa-square-pen {
  --fa: "";
}

.fa-suitcase-rolling {
  --fa: "";
}

.fa-person-circle-exclamation {
  --fa: "";
}

.fa-chevron-down {
  --fa: "";
}

.fa-battery, .fa-battery-5, .fa-battery-full {
  --fa: "";
}

.fa-skull-crossbones {
  --fa: "";
}

.fa-code-compare {
  --fa: "";
}

.fa-list-dots, .fa-list-ul {
  --fa: "";
}

.fa-school-lock {
  --fa: "";
}

.fa-tower-cell {
  --fa: "";
}

.fa-down-long, .fa-long-arrow-alt-down {
  --fa: "";
}

.fa-ranking-star {
  --fa: "";
}

.fa-chess-king {
  --fa: "";
}

.fa-person-harassing {
  --fa: "";
}

.fa-brazilian-real-sign {
  --fa: "";
}

.fa-landmark-alt, .fa-landmark-dome {
  --fa: "";
}

.fa-arrow-up {
  --fa: "";
}

.fa-television, .fa-tv, .fa-tv-alt {
  --fa: "";
}

.fa-shrimp {
  --fa: "";
}

.fa-list-check, .fa-tasks {
  --fa: "";
}

.fa-jug-detergent {
  --fa: "";
}

.fa-circle-user, .fa-user-circle {
  --fa: "";
}

.fa-user-shield {
  --fa: "";
}

.fa-wind {
  --fa: "";
}

.fa-car-burst, .fa-car-crash {
  --fa: "";
}

.fa-y {
  --fa: "Y";
}

.fa-person-snowboarding, .fa-snowboarding {
  --fa: "";
}

.fa-shipping-fast, .fa-truck-fast {
  --fa: "";
}

.fa-fish {
  --fa: "";
}

.fa-user-graduate {
  --fa: "";
}

.fa-adjust, .fa-circle-half-stroke {
  --fa: "";
}

.fa-clapperboard {
  --fa: "";
}

.fa-circle-radiation, .fa-radiation-alt {
  --fa: "";
}

.fa-baseball, .fa-baseball-ball {
  --fa: "";
}

.fa-jet-fighter-up {
  --fa: "";
}

.fa-diagram-project, .fa-project-diagram {
  --fa: "";
}

.fa-copy {
  --fa: "";
}

.fa-volume-mute, .fa-volume-times, .fa-volume-xmark {
  --fa: "";
}

.fa-hand-sparkles {
  --fa: "";
}

.fa-grip, .fa-grip-horizontal {
  --fa: "";
}

.fa-share-from-square, .fa-share-square {
  --fa: "";
}

.fa-child-combatant, .fa-child-rifle {
  --fa: "";
}

.fa-gun {
  --fa: "";
}

.fa-phone-square, .fa-square-phone {
  --fa: "";
}

.fa-add, .fa-plus {
  --fa: "+";
}

.fa-expand {
  --fa: "";
}

.fa-computer {
  --fa: "";
}

.fa-close, .fa-multiply, .fa-remove, .fa-times, .fa-xmark {
  --fa: "";
}

.fa-arrows, .fa-arrows-up-down-left-right {
  --fa: "";
}

.fa-chalkboard-teacher, .fa-chalkboard-user {
  --fa: "";
}

.fa-peso-sign {
  --fa: "";
}

.fa-building-shield {
  --fa: "";
}

.fa-baby {
  --fa: "";
}

.fa-users-line {
  --fa: "";
}

.fa-quote-left, .fa-quote-left-alt {
  --fa: "";
}

.fa-tractor {
  --fa: "";
}

.fa-trash-arrow-up, .fa-trash-restore {
  --fa: "";
}

.fa-arrow-down-up-lock {
  --fa: "";
}

.fa-lines-leaning {
  --fa: "";
}

.fa-ruler-combined {
  --fa: "";
}

.fa-copyright {
  --fa: "";
}

.fa-equals {
  --fa: "=";
}

.fa-blender {
  --fa: "";
}

.fa-teeth {
  --fa: "";
}

.fa-ils, .fa-shekel, .fa-shekel-sign, .fa-sheqel, .fa-sheqel-sign {
  --fa: "";
}

.fa-map {
  --fa: "";
}

.fa-rocket {
  --fa: "";
}

.fa-photo-film, .fa-photo-video {
  --fa: "";
}

.fa-folder-minus {
  --fa: "";
}

.fa-hexagon-nodes-bolt {
  --fa: "";
}

.fa-store {
  --fa: "";
}

.fa-arrow-trend-up {
  --fa: "";
}

.fa-plug-circle-minus {
  --fa: "";
}

.fa-sign, .fa-sign-hanging {
  --fa: "";
}

.fa-bezier-curve {
  --fa: "";
}

.fa-bell-slash {
  --fa: "";
}

.fa-tablet, .fa-tablet-android {
  --fa: "";
}

.fa-school-flag {
  --fa: "";
}

.fa-fill {
  --fa: "";
}

.fa-angle-up {
  --fa: "";
}

.fa-drumstick-bite {
  --fa: "";
}

.fa-holly-berry {
  --fa: "";
}

.fa-chevron-left {
  --fa: "";
}

.fa-bacteria {
  --fa: "";
}

.fa-hand-lizard {
  --fa: "";
}

.fa-notdef {
  --fa: "";
}

.fa-disease {
  --fa: "";
}

.fa-briefcase-medical {
  --fa: "";
}

.fa-genderless {
  --fa: "";
}

.fa-chevron-right {
  --fa: "";
}

.fa-retweet {
  --fa: "";
}

.fa-car-alt, .fa-car-rear {
  --fa: "";
}

.fa-pump-soap {
  --fa: "";
}

.fa-video-slash {
  --fa: "";
}

.fa-battery-2, .fa-battery-quarter {
  --fa: "";
}

.fa-radio {
  --fa: "";
}

.fa-baby-carriage, .fa-carriage-baby {
  --fa: "";
}

.fa-traffic-light {
  --fa: "";
}

.fa-thermometer {
  --fa: "";
}

.fa-vr-cardboard {
  --fa: "";
}

.fa-hand-middle-finger {
  --fa: "";
}

.fa-percent, .fa-percentage {
  --fa: "%";
}

.fa-truck-moving {
  --fa: "";
}

.fa-glass-water-droplet {
  --fa: "";
}

.fa-display {
  --fa: "";
}

.fa-face-smile, .fa-smile {
  --fa: "";
}

.fa-thumb-tack, .fa-thumbtack {
  --fa: "";
}

.fa-trophy {
  --fa: "";
}

.fa-person-praying, .fa-pray {
  --fa: "";
}

.fa-hammer {
  --fa: "";
}

.fa-hand-peace {
  --fa: "";
}

.fa-rotate, .fa-sync-alt {
  --fa: "";
}

.fa-spinner {
  --fa: "";
}

.fa-robot {
  --fa: "";
}

.fa-peace {
  --fa: "";
}

.fa-cogs, .fa-gears {
  --fa: "";
}

.fa-warehouse {
  --fa: "";
}

.fa-arrow-up-right-dots {
  --fa: "";
}

.fa-splotch {
  --fa: "";
}

.fa-face-grin-hearts, .fa-grin-hearts {
  --fa: "";
}

.fa-dice-four {
  --fa: "";
}

.fa-sim-card {
  --fa: "";
}

.fa-transgender, .fa-transgender-alt {
  --fa: "";
}

.fa-mercury {
  --fa: "";
}

.fa-arrow-turn-down, .fa-level-down {
  --fa: "";
}

.fa-person-falling-burst {
  --fa: "";
}

.fa-award {
  --fa: "";
}

.fa-ticket-alt, .fa-ticket-simple {
  --fa: "";
}

.fa-building {
  --fa: "";
}

.fa-angle-double-left, .fa-angles-left {
  --fa: "";
}

.fa-qrcode {
  --fa: "";
}

.fa-clock-rotate-left, .fa-history {
  --fa: "";
}

.fa-face-grin-beam-sweat, .fa-grin-beam-sweat {
  --fa: "";
}

.fa-arrow-right-from-file, .fa-file-export {
  --fa: "";
}

.fa-shield, .fa-shield-blank {
  --fa: "";
}

.fa-arrow-up-short-wide, .fa-sort-amount-up-alt {
  --fa: "";
}

.fa-comment-nodes {
  --fa: "";
}

.fa-house-medical {
  --fa: "";
}

.fa-golf-ball, .fa-golf-ball-tee {
  --fa: "";
}

.fa-chevron-circle-left, .fa-circle-chevron-left {
  --fa: "";
}

.fa-house-chimney-window {
  --fa: "";
}

.fa-pen-nib {
  --fa: "";
}

.fa-tent-arrow-turn-left {
  --fa: "";
}

.fa-tents {
  --fa: "";
}

.fa-magic, .fa-wand-magic {
  --fa: "";
}

.fa-dog {
  --fa: "";
}

.fa-carrot {
  --fa: "";
}

.fa-moon {
  --fa: "";
}

.fa-wine-glass-alt, .fa-wine-glass-empty {
  --fa: "";
}

.fa-cheese {
  --fa: "";
}

.fa-yin-yang {
  --fa: "";
}

.fa-music {
  --fa: "";
}

.fa-code-commit {
  --fa: "";
}

.fa-temperature-low {
  --fa: "";
}

.fa-biking, .fa-person-biking {
  --fa: "";
}

.fa-broom {
  --fa: "";
}

.fa-shield-heart {
  --fa: "";
}

.fa-gopuram {
  --fa: "";
}

.fa-earth-oceania, .fa-globe-oceania {
  --fa: "";
}

.fa-square-xmark, .fa-times-square, .fa-xmark-square {
  --fa: "";
}

.fa-hashtag {
  --fa: "#";
}

.fa-expand-alt, .fa-up-right-and-down-left-from-center {
  --fa: "";
}

.fa-oil-can {
  --fa: "";
}

.fa-t {
  --fa: "T";
}

.fa-hippo {
  --fa: "";
}

.fa-chart-column {
  --fa: "";
}

.fa-infinity {
  --fa: "";
}

.fa-vial-circle-check {
  --fa: "";
}

.fa-person-arrow-down-to-line {
  --fa: "";
}

.fa-voicemail {
  --fa: "";
}

.fa-fan {
  --fa: "";
}

.fa-person-walking-luggage {
  --fa: "";
}

.fa-arrows-alt-v, .fa-up-down {
  --fa: "";
}

.fa-cloud-moon-rain {
  --fa: "";
}

.fa-calendar {
  --fa: "";
}

.fa-trailer {
  --fa: "";
}

.fa-bahai, .fa-haykal {
  --fa: "";
}

.fa-sd-card {
  --fa: "";
}

.fa-dragon {
  --fa: "";
}

.fa-shoe-prints {
  --fa: "";
}

.fa-circle-plus, .fa-plus-circle {
  --fa: "";
}

.fa-face-grin-tongue-wink, .fa-grin-tongue-wink {
  --fa: "";
}

.fa-hand-holding {
  --fa: "";
}

.fa-plug-circle-exclamation {
  --fa: "";
}

.fa-chain-broken, .fa-chain-slash, .fa-link-slash, .fa-unlink {
  --fa: "";
}

.fa-clone {
  --fa: "";
}

.fa-person-walking-arrow-loop-left {
  --fa: "";
}

.fa-arrow-up-z-a, .fa-sort-alpha-up-alt {
  --fa: "";
}

.fa-fire-alt, .fa-fire-flame-curved {
  --fa: "";
}

.fa-tornado {
  --fa: "";
}

.fa-file-circle-plus {
  --fa: "";
}

.fa-book-quran, .fa-quran {
  --fa: "";
}

.fa-anchor {
  --fa: "";
}

.fa-border-all {
  --fa: "";
}

.fa-angry, .fa-face-angry {
  --fa: "";
}

.fa-cookie-bite {
  --fa: "";
}

.fa-arrow-trend-down {
  --fa: "";
}

.fa-feed, .fa-rss {
  --fa: "";
}

.fa-draw-polygon {
  --fa: "";
}

.fa-balance-scale, .fa-scale-balanced {
  --fa: "";
}

.fa-gauge-simple-high, .fa-tachometer, .fa-tachometer-fast {
  --fa: "";
}

.fa-shower {
  --fa: "";
}

.fa-desktop, .fa-desktop-alt {
  --fa: "";
}

.fa-m {
  --fa: "M";
}

.fa-table-list, .fa-th-list {
  --fa: "";
}

.fa-comment-sms, .fa-sms {
  --fa: "";
}

.fa-book {
  --fa: "";
}

.fa-user-plus {
  --fa: "";
}

.fa-check {
  --fa: "";
}

.fa-battery-4, .fa-battery-three-quarters {
  --fa: "";
}

.fa-house-circle-check {
  --fa: "";
}

.fa-angle-left {
  --fa: "";
}

.fa-diagram-successor {
  --fa: "";
}

.fa-truck-arrow-right {
  --fa: "";
}

.fa-arrows-split-up-and-left {
  --fa: "";
}

.fa-fist-raised, .fa-hand-fist {
  --fa: "";
}

.fa-cloud-moon {
  --fa: "";
}

.fa-briefcase {
  --fa: "";
}

.fa-person-falling {
  --fa: "";
}

.fa-image-portrait, .fa-portrait {
  --fa: "";
}

.fa-user-tag {
  --fa: "";
}

.fa-rug {
  --fa: "";
}

.fa-earth-europe, .fa-globe-europe {
  --fa: "";
}

.fa-cart-flatbed-suitcase, .fa-luggage-cart {
  --fa: "";
}

.fa-rectangle-times, .fa-rectangle-xmark, .fa-times-rectangle, .fa-window-close {
  --fa: "";
}

.fa-baht-sign {
  --fa: "";
}

.fa-book-open {
  --fa: "";
}

.fa-book-journal-whills, .fa-journal-whills {
  --fa: "";
}

.fa-handcuffs {
  --fa: "";
}

.fa-exclamation-triangle, .fa-triangle-exclamation, .fa-warning {
  --fa: "";
}

.fa-database {
  --fa: "";
}

.fa-mail-forward, .fa-share {
  --fa: "";
}

.fa-bottle-droplet {
  --fa: "";
}

.fa-mask-face {
  --fa: "";
}

.fa-hill-rockslide {
  --fa: "";
}

.fa-exchange-alt, .fa-right-left {
  --fa: "";
}

.fa-paper-plane {
  --fa: "";
}

.fa-road-circle-exclamation {
  --fa: "";
}

.fa-dungeon {
  --fa: "";
}

.fa-align-right {
  --fa: "";
}

.fa-money-bill-1-wave, .fa-money-bill-wave-alt {
  --fa: "";
}

.fa-life-ring {
  --fa: "";
}

.fa-hands, .fa-sign-language, .fa-signing {
  --fa: "";
}

.fa-calendar-day {
  --fa: "";
}

.fa-ladder-water, .fa-swimming-pool, .fa-water-ladder {
  --fa: "";
}

.fa-arrows-up-down, .fa-arrows-v {
  --fa: "";
}

.fa-face-grimace, .fa-grimace {
  --fa: "";
}

.fa-wheelchair-alt, .fa-wheelchair-move {
  --fa: "";
}

.fa-level-down-alt, .fa-turn-down {
  --fa: "";
}

.fa-person-walking-arrow-right {
  --fa: "";
}

.fa-envelope-square, .fa-square-envelope {
  --fa: "";
}

.fa-dice {
  --fa: "";
}

.fa-bowling-ball {
  --fa: "";
}

.fa-brain {
  --fa: "";
}

.fa-band-aid, .fa-bandage {
  --fa: "";
}

.fa-calendar-minus {
  --fa: "";
}

.fa-circle-xmark, .fa-times-circle, .fa-xmark-circle {
  --fa: "";
}

.fa-gifts {
  --fa: "";
}

.fa-hotel {
  --fa: "";
}

.fa-earth-asia, .fa-globe-asia {
  --fa: "";
}

.fa-id-card-alt, .fa-id-card-clip {
  --fa: "";
}

.fa-magnifying-glass-plus, .fa-search-plus {
  --fa: "";
}

.fa-thumbs-up {
  --fa: "";
}

.fa-user-clock {
  --fa: "";
}

.fa-allergies, .fa-hand-dots {
  --fa: "";
}

.fa-file-invoice {
  --fa: "";
}

.fa-window-minimize {
  --fa: "";
}

.fa-coffee, .fa-mug-saucer {
  --fa: "";
}

.fa-brush {
  --fa: "";
}

.fa-file-half-dashed {
  --fa: "";
}

.fa-mask {
  --fa: "";
}

.fa-magnifying-glass-minus, .fa-search-minus {
  --fa: "";
}

.fa-ruler-vertical {
  --fa: "";
}

.fa-user-alt, .fa-user-large {
  --fa: "";
}

.fa-train-tram {
  --fa: "";
}

.fa-user-nurse {
  --fa: "";
}

.fa-syringe {
  --fa: "";
}

.fa-cloud-sun {
  --fa: "";
}

.fa-stopwatch-20 {
  --fa: "";
}

.fa-square-full {
  --fa: "";
}

.fa-magnet {
  --fa: "";
}

.fa-jar {
  --fa: "";
}

.fa-note-sticky, .fa-sticky-note {
  --fa: "";
}

.fa-bug-slash {
  --fa: "";
}

.fa-arrow-up-from-water-pump {
  --fa: "";
}

.fa-bone {
  --fa: "";
}

.fa-table-cells-row-unlock {
  --fa: "";
}

.fa-user-injured {
  --fa: "";
}

.fa-face-sad-tear, .fa-sad-tear {
  --fa: "";
}

.fa-plane {
  --fa: "";
}

.fa-tent-arrows-down {
  --fa: "";
}

.fa-exclamation {
  --fa: "!";
}

.fa-arrows-spin {
  --fa: "";
}

.fa-print {
  --fa: "";
}

.fa-try, .fa-turkish-lira, .fa-turkish-lira-sign {
  --fa: "";
}

.fa-dollar, .fa-dollar-sign, .fa-usd {
  --fa: "$";
}

.fa-x {
  --fa: "X";
}

.fa-magnifying-glass-dollar, .fa-search-dollar {
  --fa: "";
}

.fa-users-cog, .fa-users-gear {
  --fa: "";
}

.fa-person-military-pointing {
  --fa: "";
}

.fa-bank, .fa-building-columns, .fa-institution, .fa-museum, .fa-university {
  --fa: "";
}

.fa-umbrella {
  --fa: "";
}

.fa-trowel {
  --fa: "";
}

.fa-d {
  --fa: "D";
}

.fa-stapler {
  --fa: "";
}

.fa-masks-theater, .fa-theater-masks {
  --fa: "";
}

.fa-kip-sign {
  --fa: "";
}

.fa-hand-point-left {
  --fa: "";
}

.fa-handshake-alt, .fa-handshake-simple {
  --fa: "";
}

.fa-fighter-jet, .fa-jet-fighter {
  --fa: "";
}

.fa-share-alt-square, .fa-square-share-nodes {
  --fa: "";
}

.fa-barcode {
  --fa: "";
}

.fa-plus-minus {
  --fa: "";
}

.fa-video, .fa-video-camera {
  --fa: "";
}

.fa-graduation-cap, .fa-mortar-board {
  --fa: "";
}

.fa-hand-holding-medical {
  --fa: "";
}

.fa-person-circle-check {
  --fa: "";
}

.fa-level-up-alt, .fa-turn-up {
  --fa: "";
}

.fa-sr-only, .fa-sr-only-focusable:not(:focus), .sr-only, .sr-only-focusable:not(:focus) {
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  position: absolute;
  overflow: hidden;
}

:host, :root {
  --fa-style-family-brands: "Font Awesome 6 Brands";
  --fa-font-brands: normal 400 1em / 1 "Font Awesome 6 Brands";
}

@font-face {
  font-family: "Font Awesome 6 Brands";
  font-style: normal;
  font-weight: 400;
  font-display: block;
  src: url("../media/fa-brands-400.4404d771.woff2") format("woff2"), url("../media/fa-brands-400.5cea5444.ttf") format("truetype");
}

.fa-brands, .fab {
  font-weight: 400;
}

.fa-monero {
  --fa: "";
}

.fa-hooli {
  --fa: "";
}

.fa-yelp {
  --fa: "";
}

.fa-cc-visa {
  --fa: "";
}

.fa-lastfm {
  --fa: "";
}

.fa-shopware {
  --fa: "";
}

.fa-creative-commons-nc {
  --fa: "";
}

.fa-aws {
  --fa: "";
}

.fa-redhat {
  --fa: "";
}

.fa-yoast {
  --fa: "";
}

.fa-cloudflare {
  --fa: "";
}

.fa-ups {
  --fa: "";
}

.fa-pixiv {
  --fa: "";
}

.fa-wpexplorer {
  --fa: "";
}

.fa-dyalog {
  --fa: "";
}

.fa-bity {
  --fa: "";
}

.fa-stackpath {
  --fa: "";
}

.fa-buysellads {
  --fa: "";
}

.fa-first-order {
  --fa: "";
}

.fa-modx {
  --fa: "";
}

.fa-guilded {
  --fa: "";
}

.fa-vnv {
  --fa: "";
}

.fa-js-square, .fa-square-js {
  --fa: "";
}

.fa-microsoft {
  --fa: "";
}

.fa-qq {
  --fa: "";
}

.fa-orcid {
  --fa: "";
}

.fa-java {
  --fa: "";
}

.fa-invision {
  --fa: "";
}

.fa-creative-commons-pd-alt {
  --fa: "";
}

.fa-centercode {
  --fa: "";
}

.fa-glide-g {
  --fa: "";
}

.fa-drupal {
  --fa: "";
}

.fa-jxl {
  --fa: "";
}

.fa-dart-lang {
  --fa: "";
}

.fa-hire-a-helper {
  --fa: "";
}

.fa-creative-commons-by {
  --fa: "";
}

.fa-unity {
  --fa: "";
}

.fa-whmcs {
  --fa: "";
}

.fa-rocketchat {
  --fa: "";
}

.fa-vk {
  --fa: "";
}

.fa-untappd {
  --fa: "";
}

.fa-mailchimp {
  --fa: "";
}

.fa-css3-alt {
  --fa: "";
}

.fa-reddit-square, .fa-square-reddit {
  --fa: "";
}

.fa-vimeo-v {
  --fa: "";
}

.fa-contao {
  --fa: "";
}

.fa-square-font-awesome {
  --fa: "";
}

.fa-deskpro {
  --fa: "";
}

.fa-brave {
  --fa: "";
}

.fa-sistrix {
  --fa: "";
}

.fa-instagram-square, .fa-square-instagram {
  --fa: "";
}

.fa-battle-net {
  --fa: "";
}

.fa-the-red-yeti {
  --fa: "";
}

.fa-hacker-news-square, .fa-square-hacker-news {
  --fa: "";
}

.fa-edge {
  --fa: "";
}

.fa-threads {
  --fa: "";
}

.fa-napster {
  --fa: "";
}

.fa-snapchat-square, .fa-square-snapchat {
  --fa: "";
}

.fa-google-plus-g {
  --fa: "";
}

.fa-artstation {
  --fa: "";
}

.fa-markdown {
  --fa: "";
}

.fa-sourcetree {
  --fa: "";
}

.fa-google-plus {
  --fa: "";
}

.fa-diaspora {
  --fa: "";
}

.fa-foursquare {
  --fa: "";
}

.fa-stack-overflow {
  --fa: "";
}

.fa-github-alt {
  --fa: "";
}

.fa-phoenix-squadron {
  --fa: "";
}

.fa-pagelines {
  --fa: "";
}

.fa-algolia {
  --fa: "";
}

.fa-red-river {
  --fa: "";
}

.fa-creative-commons-sa {
  --fa: "";
}

.fa-safari {
  --fa: "";
}

.fa-google {
  --fa: "";
}

.fa-font-awesome-alt, .fa-square-font-awesome-stroke {
  --fa: "";
}

.fa-atlassian {
  --fa: "";
}

.fa-linkedin-in {
  --fa: "";
}

.fa-digital-ocean {
  --fa: "";
}

.fa-nimblr {
  --fa: "";
}

.fa-chromecast {
  --fa: "";
}

.fa-evernote {
  --fa: "";
}

.fa-hacker-news {
  --fa: "";
}

.fa-creative-commons-sampling {
  --fa: "";
}

.fa-adversal {
  --fa: "";
}

.fa-creative-commons {
  --fa: "";
}

.fa-watchman-monitoring {
  --fa: "";
}

.fa-fonticons {
  --fa: "";
}

.fa-weixin {
  --fa: "";
}

.fa-shirtsinbulk {
  --fa: "";
}

.fa-codepen {
  --fa: "";
}

.fa-git-alt {
  --fa: "";
}

.fa-lyft {
  --fa: "";
}

.fa-rev {
  --fa: "";
}

.fa-windows {
  --fa: "";
}

.fa-wizards-of-the-coast {
  --fa: "";
}

.fa-square-viadeo, .fa-viadeo-square {
  --fa: "";
}

.fa-meetup {
  --fa: "";
}

.fa-centos {
  --fa: "";
}

.fa-adn {
  --fa: "";
}

.fa-cloudsmith {
  --fa: "";
}

.fa-opensuse {
  --fa: "";
}

.fa-pied-piper-alt {
  --fa: "";
}

.fa-dribbble-square, .fa-square-dribbble {
  --fa: "";
}

.fa-codiepie {
  --fa: "";
}

.fa-node {
  --fa: "";
}

.fa-mix {
  --fa: "";
}

.fa-steam {
  --fa: "";
}

.fa-cc-apple-pay {
  --fa: "";
}

.fa-scribd {
  --fa: "";
}

.fa-debian {
  --fa: "";
}

.fa-openid {
  --fa: "";
}

.fa-instalod {
  --fa: "";
}

.fa-files-pinwheel {
  --fa: "";
}

.fa-expeditedssl {
  --fa: "";
}

.fa-sellcast {
  --fa: "";
}

.fa-square-twitter, .fa-twitter-square {
  --fa: "";
}

.fa-r-project {
  --fa: "";
}

.fa-delicious {
  --fa: "";
}

.fa-freebsd {
  --fa: "";
}

.fa-vuejs {
  --fa: "";
}

.fa-accusoft {
  --fa: "";
}

.fa-ioxhost {
  --fa: "";
}

.fa-fonticons-fi {
  --fa: "";
}

.fa-app-store {
  --fa: "";
}

.fa-cc-mastercard {
  --fa: "";
}

.fa-itunes-note {
  --fa: "";
}

.fa-golang {
  --fa: "";
}

.fa-kickstarter, .fa-square-kickstarter {
  --fa: "";
}

.fa-grav {
  --fa: "";
}

.fa-weibo {
  --fa: "";
}

.fa-uncharted {
  --fa: "";
}

.fa-firstdraft {
  --fa: "";
}

.fa-square-youtube, .fa-youtube-square {
  --fa: "";
}

.fa-wikipedia-w {
  --fa: "";
}

.fa-rendact, .fa-wpressr {
  --fa: "";
}

.fa-angellist {
  --fa: "";
}

.fa-galactic-republic {
  --fa: "";
}

.fa-nfc-directional {
  --fa: "";
}

.fa-skype {
  --fa: "";
}

.fa-joget {
  --fa: "";
}

.fa-fedora {
  --fa: "";
}

.fa-stripe-s {
  --fa: "";
}

.fa-meta {
  --fa: "";
}

.fa-laravel {
  --fa: "";
}

.fa-hotjar {
  --fa: "";
}

.fa-bluetooth-b {
  --fa: "";
}

.fa-square-letterboxd {
  --fa: "";
}

.fa-sticker-mule {
  --fa: "";
}

.fa-creative-commons-zero {
  --fa: "";
}

.fa-hips {
  --fa: "";
}

.fa-css {
  --fa: "";
}

.fa-behance {
  --fa: "";
}

.fa-reddit {
  --fa: "";
}

.fa-discord {
  --fa: "";
}

.fa-chrome {
  --fa: "";
}

.fa-app-store-ios {
  --fa: "";
}

.fa-cc-discover {
  --fa: "";
}

.fa-wpbeginner {
  --fa: "";
}

.fa-confluence {
  --fa: "";
}

.fa-shoelace {
  --fa: "";
}

.fa-mdb {
  --fa: "";
}

.fa-dochub {
  --fa: "";
}

.fa-accessible-icon {
  --fa: "";
}

.fa-ebay {
  --fa: "";
}

.fa-amazon {
  --fa: "";
}

.fa-unsplash {
  --fa: "";
}

.fa-yarn {
  --fa: "";
}

.fa-square-steam, .fa-steam-square {
  --fa: "";
}

.fa-500px {
  --fa: "";
}

.fa-square-vimeo, .fa-vimeo-square {
  --fa: "";
}

.fa-asymmetrik {
  --fa: "";
}

.fa-font-awesome, .fa-font-awesome-flag, .fa-font-awesome-logo-full {
  --fa: "";
}

.fa-gratipay {
  --fa: "";
}

.fa-apple {
  --fa: "";
}

.fa-hive {
  --fa: "";
}

.fa-gitkraken {
  --fa: "";
}

.fa-keybase {
  --fa: "";
}

.fa-apple-pay {
  --fa: "";
}

.fa-padlet {
  --fa: "";
}

.fa-amazon-pay {
  --fa: "";
}

.fa-github-square, .fa-square-github {
  --fa: "";
}

.fa-stumbleupon {
  --fa: "";
}

.fa-fedex {
  --fa: "";
}

.fa-phoenix-framework {
  --fa: "";
}

.fa-shopify {
  --fa: "";
}

.fa-neos {
  --fa: "";
}

.fa-square-threads {
  --fa: "";
}

.fa-hackerrank {
  --fa: "";
}

.fa-researchgate {
  --fa: "";
}

.fa-swift {
  --fa: "";
}

.fa-angular {
  --fa: "";
}

.fa-speakap {
  --fa: "";
}

.fa-angrycreative {
  --fa: "";
}

.fa-y-combinator {
  --fa: "";
}

.fa-empire {
  --fa: "";
}

.fa-envira {
  --fa: "";
}

.fa-google-scholar {
  --fa: "";
}

.fa-gitlab-square, .fa-square-gitlab {
  --fa: "";
}

.fa-studiovinari {
  --fa: "";
}

.fa-pied-piper {
  --fa: "";
}

.fa-wordpress {
  --fa: "";
}

.fa-product-hunt {
  --fa: "";
}

.fa-firefox {
  --fa: "";
}

.fa-linode {
  --fa: "";
}

.fa-goodreads {
  --fa: "";
}

.fa-odnoklassniki-square, .fa-square-odnoklassniki {
  --fa: "";
}

.fa-jsfiddle {
  --fa: "";
}

.fa-sith {
  --fa: "";
}

.fa-themeisle {
  --fa: "";
}

.fa-page4 {
  --fa: "";
}

.fa-hashnode {
  --fa: "";
}

.fa-react {
  --fa: "";
}

.fa-cc-paypal {
  --fa: "";
}

.fa-squarespace {
  --fa: "";
}

.fa-cc-stripe {
  --fa: "";
}

.fa-creative-commons-share {
  --fa: "";
}

.fa-bitcoin {
  --fa: "";
}

.fa-keycdn {
  --fa: "";
}

.fa-opera {
  --fa: "";
}

.fa-itch-io {
  --fa: "";
}

.fa-umbraco {
  --fa: "";
}

.fa-galactic-senate {
  --fa: "";
}

.fa-ubuntu {
  --fa: "";
}

.fa-draft2digital {
  --fa: "";
}

.fa-stripe {
  --fa: "";
}

.fa-houzz {
  --fa: "";
}

.fa-gg {
  --fa: "";
}

.fa-dhl {
  --fa: "";
}

.fa-pinterest-square, .fa-square-pinterest {
  --fa: "";
}

.fa-xing {
  --fa: "";
}

.fa-blackberry {
  --fa: "";
}

.fa-creative-commons-pd {
  --fa: "";
}

.fa-playstation {
  --fa: "";
}

.fa-quinscape {
  --fa: "";
}

.fa-less {
  --fa: "";
}

.fa-blogger-b {
  --fa: "";
}

.fa-opencart {
  --fa: "";
}

.fa-vine {
  --fa: "";
}

.fa-signal-messenger {
  --fa: "";
}

.fa-paypal {
  --fa: "";
}

.fa-gitlab {
  --fa: "";
}

.fa-typo3 {
  --fa: "";
}

.fa-reddit-alien {
  --fa: "";
}

.fa-yahoo {
  --fa: "";
}

.fa-dailymotion {
  --fa: "";
}

.fa-affiliatetheme {
  --fa: "";
}

.fa-pied-piper-pp {
  --fa: "";
}

.fa-bootstrap {
  --fa: "";
}

.fa-odnoklassniki {
  --fa: "";
}

.fa-nfc-symbol {
  --fa: "";
}

.fa-mintbit {
  --fa: "";
}

.fa-ethereum {
  --fa: "";
}

.fa-speaker-deck {
  --fa: "";
}

.fa-creative-commons-nc-eu {
  --fa: "";
}

.fa-patreon {
  --fa: "";
}

.fa-avianex {
  --fa: "";
}

.fa-ello {
  --fa: "";
}

.fa-gofore {
  --fa: "";
}

.fa-bimobject {
  --fa: "";
}

.fa-brave-reverse {
  --fa: "";
}

.fa-facebook-f {
  --fa: "";
}

.fa-google-plus-square, .fa-square-google-plus {
  --fa: "";
}

.fa-web-awesome {
  --fa: "";
}

.fa-mandalorian {
  --fa: "";
}

.fa-first-order-alt {
  --fa: "";
}

.fa-osi {
  --fa: "";
}

.fa-google-wallet {
  --fa: "";
}

.fa-d-and-d-beyond {
  --fa: "";
}

.fa-periscope {
  --fa: "";
}

.fa-fulcrum {
  --fa: "";
}

.fa-cloudscale {
  --fa: "";
}

.fa-forumbee {
  --fa: "";
}

.fa-mizuni {
  --fa: "";
}

.fa-schlix {
  --fa: "";
}

.fa-square-xing, .fa-xing-square {
  --fa: "";
}

.fa-bandcamp {
  --fa: "";
}

.fa-wpforms {
  --fa: "";
}

.fa-cloudversify {
  --fa: "";
}

.fa-usps {
  --fa: "";
}

.fa-megaport {
  --fa: "";
}

.fa-magento {
  --fa: "";
}

.fa-spotify {
  --fa: "";
}

.fa-optin-monster {
  --fa: "";
}

.fa-fly {
  --fa: "";
}

.fa-square-bluesky {
  --fa: "";
}

.fa-aviato {
  --fa: "";
}

.fa-itunes {
  --fa: "";
}

.fa-cuttlefish {
  --fa: "";
}

.fa-blogger {
  --fa: "";
}

.fa-flickr {
  --fa: "";
}

.fa-viber {
  --fa: "";
}

.fa-soundcloud {
  --fa: "";
}

.fa-digg {
  --fa: "";
}

.fa-tencent-weibo {
  --fa: "";
}

.fa-letterboxd {
  --fa: "";
}

.fa-symfony {
  --fa: "";
}

.fa-maxcdn {
  --fa: "";
}

.fa-etsy {
  --fa: "";
}

.fa-facebook-messenger {
  --fa: "";
}

.fa-audible {
  --fa: "";
}

.fa-think-peaks {
  --fa: "";
}

.fa-bilibili {
  --fa: "";
}

.fa-erlang {
  --fa: "";
}

.fa-x-twitter {
  --fa: "";
}

.fa-cotton-bureau {
  --fa: "";
}

.fa-dashcube {
  --fa: "";
}

.fa-42-group, .fa-innosoft {
  --fa: "";
}

.fa-stack-exchange {
  --fa: "";
}

.fa-elementor {
  --fa: "";
}

.fa-pied-piper-square, .fa-square-pied-piper {
  --fa: "";
}

.fa-creative-commons-nd {
  --fa: "";
}

.fa-palfed {
  --fa: "";
}

.fa-superpowers {
  --fa: "";
}

.fa-resolving {
  --fa: "";
}

.fa-xbox {
  --fa: "";
}

.fa-square-web-awesome-stroke {
  --fa: "";
}

.fa-searchengin {
  --fa: "";
}

.fa-tiktok {
  --fa: "";
}

.fa-facebook-square, .fa-square-facebook {
  --fa: "";
}

.fa-renren {
  --fa: "";
}

.fa-linux {
  --fa: "";
}

.fa-glide {
  --fa: "";
}

.fa-linkedin {
  --fa: "";
}

.fa-hubspot {
  --fa: "";
}

.fa-deploydog {
  --fa: "";
}

.fa-twitch {
  --fa: "";
}

.fa-flutter {
  --fa: "";
}

.fa-ravelry {
  --fa: "";
}

.fa-mixer {
  --fa: "";
}

.fa-lastfm-square, .fa-square-lastfm {
  --fa: "";
}

.fa-vimeo {
  --fa: "";
}

.fa-mendeley {
  --fa: "";
}

.fa-uniregistry {
  --fa: "";
}

.fa-figma {
  --fa: "";
}

.fa-creative-commons-remix {
  --fa: "";
}

.fa-cc-amazon-pay {
  --fa: "";
}

.fa-dropbox {
  --fa: "";
}

.fa-instagram {
  --fa: "";
}

.fa-cmplid {
  --fa: "";
}

.fa-upwork {
  --fa: "";
}

.fa-facebook {
  --fa: "";
}

.fa-gripfire {
  --fa: "";
}

.fa-jedi-order {
  --fa: "";
}

.fa-uikit {
  --fa: "";
}

.fa-fort-awesome-alt {
  --fa: "";
}

.fa-phabricator {
  --fa: "";
}

.fa-ussunnah {
  --fa: "";
}

.fa-earlybirds {
  --fa: "";
}

.fa-trade-federation {
  --fa: "";
}

.fa-autoprefixer {
  --fa: "";
}

.fa-whatsapp {
  --fa: "";
}

.fa-square-upwork {
  --fa: "";
}

.fa-slideshare {
  --fa: "";
}

.fa-google-play {
  --fa: "";
}

.fa-viadeo {
  --fa: "";
}

.fa-line {
  --fa: "";
}

.fa-google-drive {
  --fa: "";
}

.fa-servicestack {
  --fa: "";
}

.fa-simplybuilt {
  --fa: "";
}

.fa-bitbucket {
  --fa: "";
}

.fa-imdb {
  --fa: "";
}

.fa-deezer {
  --fa: "";
}

.fa-raspberry-pi {
  --fa: "";
}

.fa-jira {
  --fa: "";
}

.fa-docker {
  --fa: "";
}

.fa-screenpal {
  --fa: "";
}

.fa-bluetooth {
  --fa: "";
}

.fa-gitter {
  --fa: "";
}

.fa-d-and-d {
  --fa: "";
}

.fa-microblog {
  --fa: "";
}

.fa-cc-diners-club {
  --fa: "";
}

.fa-gg-circle {
  --fa: "";
}

.fa-pied-piper-hat {
  --fa: "";
}

.fa-kickstarter-k {
  --fa: "";
}

.fa-yandex {
  --fa: "";
}

.fa-readme {
  --fa: "";
}

.fa-html5 {
  --fa: "";
}

.fa-sellsy {
  --fa: "";
}

.fa-square-web-awesome {
  --fa: "";
}

.fa-sass {
  --fa: "";
}

.fa-wirsindhandwerk, .fa-wsh {
  --fa: "";
}

.fa-buromobelexperte {
  --fa: "";
}

.fa-salesforce {
  --fa: "";
}

.fa-octopus-deploy {
  --fa: "";
}

.fa-medapps {
  --fa: "";
}

.fa-ns8 {
  --fa: "";
}

.fa-pinterest-p {
  --fa: "";
}

.fa-apper {
  --fa: "";
}

.fa-fort-awesome {
  --fa: "";
}

.fa-waze {
  --fa: "";
}

.fa-bluesky {
  --fa: "";
}

.fa-cc-jcb {
  --fa: "";
}

.fa-snapchat, .fa-snapchat-ghost {
  --fa: "";
}

.fa-fantasy-flight-games {
  --fa: "";
}

.fa-rust {
  --fa: "";
}

.fa-wix {
  --fa: "";
}

.fa-behance-square, .fa-square-behance {
  --fa: "";
}

.fa-supple {
  --fa: "";
}

.fa-webflow {
  --fa: "";
}

.fa-rebel {
  --fa: "";
}

.fa-css3 {
  --fa: "";
}

.fa-staylinked {
  --fa: "";
}

.fa-kaggle {
  --fa: "";
}

.fa-space-awesome {
  --fa: "";
}

.fa-deviantart {
  --fa: "";
}

.fa-cpanel {
  --fa: "";
}

.fa-goodreads-g {
  --fa: "";
}

.fa-git-square, .fa-square-git {
  --fa: "";
}

.fa-square-tumblr, .fa-tumblr-square {
  --fa: "";
}

.fa-trello {
  --fa: "";
}

.fa-creative-commons-nc-jp {
  --fa: "";
}

.fa-get-pocket {
  --fa: "";
}

.fa-perbyte {
  --fa: "";
}

.fa-grunt {
  --fa: "";
}

.fa-weebly {
  --fa: "";
}

.fa-connectdevelop {
  --fa: "";
}

.fa-leanpub {
  --fa: "";
}

.fa-black-tie {
  --fa: "";
}

.fa-themeco {
  --fa: "";
}

.fa-python {
  --fa: "";
}

.fa-android {
  --fa: "";
}

.fa-bots {
  --fa: "";
}

.fa-free-code-camp {
  --fa: "";
}

.fa-hornbill {
  --fa: "";
}

.fa-js {
  --fa: "";
}

.fa-ideal {
  --fa: "";
}

.fa-git {
  --fa: "";
}

.fa-dev {
  --fa: "";
}

.fa-sketch {
  --fa: "";
}

.fa-yandex-international {
  --fa: "";
}

.fa-cc-amex {
  --fa: "";
}

.fa-uber {
  --fa: "";
}

.fa-github {
  --fa: "";
}

.fa-php {
  --fa: "";
}

.fa-alipay {
  --fa: "";
}

.fa-youtube {
  --fa: "";
}

.fa-skyatlas {
  --fa: "";
}

.fa-firefox-browser {
  --fa: "";
}

.fa-replyd {
  --fa: "";
}

.fa-suse {
  --fa: "";
}

.fa-jenkins {
  --fa: "";
}

.fa-twitter {
  --fa: "";
}

.fa-rockrms {
  --fa: "";
}

.fa-pinterest {
  --fa: "";
}

.fa-buffer {
  --fa: "";
}

.fa-npm {
  --fa: "";
}

.fa-yammer {
  --fa: "";
}

.fa-btc {
  --fa: "";
}

.fa-dribbble {
  --fa: "";
}

.fa-stumbleupon-circle {
  --fa: "";
}

.fa-internet-explorer {
  --fa: "";
}

.fa-stubber {
  --fa: "";
}

.fa-telegram, .fa-telegram-plane {
  --fa: "";
}

.fa-old-republic {
  --fa: "";
}

.fa-odysee {
  --fa: "";
}

.fa-square-whatsapp, .fa-whatsapp-square {
  --fa: "";
}

.fa-node-js {
  --fa: "";
}

.fa-edge-legacy {
  --fa: "";
}

.fa-slack, .fa-slack-hash {
  --fa: "";
}

.fa-medrt {
  --fa: "";
}

.fa-usb {
  --fa: "";
}

.fa-tumblr {
  --fa: "";
}

.fa-vaadin {
  --fa: "";
}

.fa-quora {
  --fa: "";
}

.fa-square-x-twitter {
  --fa: "";
}

.fa-reacteurope {
  --fa: "";
}

.fa-medium, .fa-medium-m {
  --fa: "";
}

.fa-amilia {
  --fa: "";
}

.fa-mixcloud {
  --fa: "";
}

.fa-flipboard {
  --fa: "";
}

.fa-viacoin {
  --fa: "";
}

.fa-critical-role {
  --fa: "";
}

.fa-sitrox {
  --fa: "";
}

.fa-discourse {
  --fa: "";
}

.fa-joomla {
  --fa: "";
}

.fa-mastodon {
  --fa: "";
}

.fa-airbnb {
  --fa: "";
}

.fa-wolf-pack-battalion {
  --fa: "";
}

.fa-buy-n-large {
  --fa: "";
}

.fa-gulp {
  --fa: "";
}

.fa-creative-commons-sampling-plus {
  --fa: "";
}

.fa-strava {
  --fa: "";
}

.fa-ember {
  --fa: "";
}

.fa-canadian-maple-leaf {
  --fa: "";
}

.fa-teamspeak {
  --fa: "";
}

.fa-pushed {
  --fa: "";
}

.fa-wordpress-simple {
  --fa: "";
}

.fa-nutritionix {
  --fa: "";
}

.fa-wodu {
  --fa: "";
}

.fa-google-pay {
  --fa: "";
}

.fa-intercom {
  --fa: "";
}

.fa-zhihu {
  --fa: "";
}

.fa-korvue {
  --fa: "";
}

.fa-pix {
  --fa: "";
}

.fa-steam-symbol {
  --fa: "";
}

:host, :root {
  --fa-font-regular: normal 400 1em / 1 "Font Awesome 6 Free";
}

@font-face {
  font-family: "Font Awesome 6 Free";
  font-style: normal;
  font-weight: 400;
  font-display: block;
  src: url("../media/fa-regular-400.bb9d7c06.woff2") format("woff2"), url("../media/fa-regular-400.f5aecc01.ttf") format("truetype");
}

.fa-regular, .far {
  font-weight: 400;
}

:host, :root {
  --fa-style-family-classic: "Font Awesome 6 Free";
  --fa-font-solid: normal 900 1em / 1 "Font Awesome 6 Free";
}

@font-face {
  font-family: "Font Awesome 6 Free";
  font-style: normal;
  font-weight: 900;
  font-display: block;
  src: url("../media/fa-solid-900.e127bcc8.woff2") format("woff2"), url("../media/fa-solid-900.9e214cea.ttf") format("truetype");
}

.fa-solid, .fas {
  font-weight: 900;
}

@font-face {
  font-family: "Font Awesome 5 Brands";
  font-display: block;
  font-weight: 400;
  src: url("../media/fa-brands-400.4404d771.woff2") format("woff2"), url("../media/fa-brands-400.5cea5444.ttf") format("truetype");
}

@font-face {
  font-family: "Font Awesome 5 Free";
  font-display: block;
  font-weight: 900;
  src: url("../media/fa-solid-900.e127bcc8.woff2") format("woff2"), url("../media/fa-solid-900.9e214cea.ttf") format("truetype");
}

@font-face {
  font-family: "Font Awesome 5 Free";
  font-display: block;
  font-weight: 400;
  src: url("../media/fa-regular-400.bb9d7c06.woff2") format("woff2"), url("../media/fa-regular-400.f5aecc01.ttf") format("truetype");
}

@font-face {
  font-family: FontAwesome;
  font-display: block;
  src: url("../media/fa-solid-900.e127bcc8.woff2") format("woff2"), url("../media/fa-solid-900.9e214cea.ttf") format("truetype");
}

@font-face {
  font-family: FontAwesome;
  font-display: block;
  src: url("../media/fa-brands-400.4404d771.woff2") format("woff2"), url("../media/fa-brands-400.5cea5444.ttf") format("truetype");
}

@font-face {
  font-family: FontAwesome;
  font-display: block;
  src: url("../media/fa-regular-400.bb9d7c06.woff2") format("woff2"), url("../media/fa-regular-400.f5aecc01.ttf") format("truetype");
  unicode-range: U+F003, U+F006, U+F014, U+F016-F017, U+F01A-F01B, U+F01D, U+F022, U+F03E, U+F044, U+F046, U+F05C-F05D, U+F06E, U+F070, U+F087-F088, U+F08A, U+F094, U+F096-F097, U+F09D, U+F0A0, U+F0A2, U+F0A4-F0A7, U+F0C5, U+F0C7, U+F0E5-F0E6, U+F0EB, U+F0F6-F0F8, U+F10C, U+F114-F115, U+F118-F11A, U+F11C-F11D, U+F133, U+F147, U+F14E, U+F150-F152, U+F185-F186, U+F18E, U+F190-F192, U+F196, U+F1C1-F1C9, U+F1D9, U+F1DB, U+F1E3, U+F1EA, U+F1F7, U+F1F9, U+F20A, U+F247-F248, U+F24A, U+F24D, U+F255-F25B, U+F25D, U+F271-F274, U+F278, U+F27B, U+F28C, U+F28E, U+F29C, U+F2B5, U+F2B7, U+F2BA, U+F2BC, U+F2BE, U+F2C0-F2C1, U+F2C3, U+F2D0, U+F2D2, U+F2D4, U+F2DC;
}

@font-face {
  font-family: FontAwesome;
  font-display: block;
  src: url("../media/fa-v4compatibility.c41cf513.woff2") format("woff2"), url("../media/fa-v4compatibility.f2a260f7.ttf") format("truetype");
  unicode-range: U+F041, U+F047, U+F065-F066, U+F07D-F07E, U+F080, U+F08B, U+F08E, U+F090, U+F09A, U+F0AC, U+F0AE, U+F0B2, U+F0D0, U+F0D6, U+F0E4, U+F0EC, U+F10A-F10B, U+F123, U+F13E, U+F148-F149, U+F14C, U+F156, U+F15E, U+F160-F161, U+F163, U+F175-F178, U+F195, U+F1F8, U+F219, U+F27A;
}

:root {
  --background: #fff;
  --foreground: #000;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

@keyframes marquee {
  0% {
    transform: translateX(100%);
  }

  100% {
    transform: translateX(-100%);
  }
}

.animate-marquee {
  min-width: 100%;
  animation: 15s linear infinite marquee;
  display: inline-block;
}

* {
  box-sizing: border-box;
}

img {
  max-width: 100%;
  height: auto;
}

@property --tw-rotate-x {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-y {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-z {
  syntax: "*";
  inherits: false
}

@property --tw-skew-x {
  syntax: "*";
  inherits: false
}

@property --tw-skew-y {
  syntax: "*";
  inherits: false
}

@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@property --tw-ease {
  syntax: "*";
  inherits: false
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes bounce {
  0%, 100% {
    animation-timing-function: cubic-bezier(.8, 0, 1, 1);
    transform: translateY(-25%);
  }

  50% {
    animation-timing-function: cubic-bezier(0, 0, .2, 1);
    transform: none;
  }
}

/*# sourceMappingURL=src_app_globals_css_f9ee138c._.single.css.map*/