"use client";

import React, { useState, useRef, useEffect } from 'react';

const ChatbotPopup = () => {
  const [showChat, setShowChat] = useState(false);
  const [messages, setMessages] = useState([
    {
      type: 'bot',
      text: '🤖 مرحباً! أنا مساعدك الذكي المتطور! 🧠\n\n✨ يمكنني مساعدتك في:\n🎯 معلومات المؤتمر الشاملة\n⚡ الطاقة المتجددة والتكنولوجيا\n🌍 تغير المناخ والبيئة\n📚 البحث العلمي والأكاديمي\n💡 أي سؤال علمي أو تقني\n\nاسألني أي شيء وسأعطيك إجابة مفصلة وذكية! 😊'
    }
  ]);
  const [inputText, setInputText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 🧠 نظام الذكاء الاصطناعي المتقدم
  const [conversationHistory, setConversationHistory] = useState([]);
  const [userProfile, setUserProfile] = useState({});
  const [learningData, setLearningData] = useState({});

  // قاعدة المعرفة الشاملة
  const knowledgeBase = {
    // معلومات المؤتمر
    conference: {
      name: 'مؤتمر الطاقة المتجددة والنفط والغاز وتغير المناخ',
      date: '25-27 نوفمبر 2025',
      location: 'طرابلس، ليبيا',
      topics: ['الطاقة المتجددة', 'النفط والغاز', 'تغير المناخ', 'التنمية المستدامة'],
      languages: ['العربية', 'الإنجليزية'],
      deadlines: {
        abstract: '15 يونيو 2025',
        fullPaper: '1 أكتوبر 2025',
        notification: '15 أغسطس 2025'
      }
    },
    // معلومات علمية
    science: {
      renewableEnergy: {
        solar: 'الطاقة الشمسية تحول ضوء الشمس إلى كهرباء باستخدام الخلايا الكهروضوئية',
        wind: 'طاقة الرياح تستخدم التوربينات لتحويل حركة الهواء إلى طاقة كهربائية',
        hydro: 'الطاقة المائية تستغل تدفق المياه لتوليد الكهرباء',
        geothermal: 'الطاقة الحرارية الأرضية تستخدم حرارة باطن الأرض'
      },
      climateChange: {
        causes: 'أسباب تغير المناخ تشمل انبعاثات غازات الدفيئة والأنشطة البشرية',
        effects: 'تأثيرات تغير المناخ تشمل ارتفاع درجات الحرارة وذوبان الأنهار الجليدية',
        solutions: 'الحلول تشمل الطاقة المتجددة وتقليل الانبعاثات والتشجير'
      }
    },
    // معلومات عامة
    general: {
      technology: 'التكنولوجيا تتطور بسرعة وتؤثر على جميع جوانب الحياة',
      education: 'التعليم هو أساس التقدم والتنمية في أي مجتمع',
      health: 'الصحة هي أهم ما يملكه الإنسان ويجب الاهتمام بها',
      economy: 'الاقتصاد يعتمد على الموارد والاستثمار والابتكار'
    }
  };

  // 🤖 نظام الذكاء الاصطناعي المتطور
  const analyzeQuestion = (question) => {
    const lowerQ = question.toLowerCase();

    // تحليل نوع السؤال
    const questionTypes = {
      what: ['ما', 'ماذا', 'what'],
      when: ['متى', 'when', 'تاريخ', 'موعد'],
      where: ['أين', 'where', 'مكان'],
      how: ['كيف', 'how', 'طريقة'],
      why: ['لماذا', 'why', 'سبب'],
      who: ['من', 'who', 'مين']
    };

    let questionType = 'general';
    for (const [type, keywords] of Object.entries(questionTypes)) {
      if (keywords.some(keyword => lowerQ.includes(keyword))) {
        questionType = type;
        break;
      }
    }

    return { questionType, originalQuestion: question, processedQuestion: lowerQ };
  };

  const generateIntelligentResponse = (analysis) => {
    const { questionType, processedQuestion } = analysis;

    // البحث في قاعدة المعرفة
    if (processedQuestion.includes('مؤتمر') || processedQuestion.includes('conference')) {
      if (questionType === 'when') {
        return `📅 المؤتمر سيقام في ${knowledgeBase.conference.date} في ${knowledgeBase.conference.location}. هذا موعد مهم جداً لا تفوته!`;
      }
      if (questionType === 'where') {
        return `📍 سيقام المؤتمر في ${knowledgeBase.conference.location}. مدينة طرابلس الجميلة ستستضيف هذا الحدث العلمي المهم.`;
      }
      if (questionType === 'what') {
        return `🎯 ${knowledgeBase.conference.name} هو حدث علمي عالمي يجمع الخبراء والباحثين لمناقشة أحدث التطورات في مجال الطاقة والبيئة.`;
      }
    }

    // الطاقة المتجددة
    if (processedQuestion.includes('طاقة') || processedQuestion.includes('energy')) {
      if (processedQuestion.includes('شمسية') || processedQuestion.includes('solar')) {
        return `☀️ ${knowledgeBase.science.renewableEnergy.solar}. هذه التقنية تشهد تطوراً مذهلاً ويمكنها تلبية احتياجات العالم من الطاقة!`;
      }
      if (processedQuestion.includes('رياح') || processedQuestion.includes('wind')) {
        return `💨 ${knowledgeBase.science.renewableEnergy.wind}. الرياح مصدر طاقة لا ينضب ونظيف تماماً!`;
      }
      return `⚡ الطاقة المتجددة هي مستقبل البشرية! تشمل الطاقة الشمسية وطاقة الرياح والمائية والحرارية الأرضية. كلها مصادر نظيفة ومستدامة.`;
    }

    // تغير المناخ
    if (processedQuestion.includes('مناخ') || processedQuestion.includes('climate')) {
      if (questionType === 'why') {
        return `🌍 ${knowledgeBase.science.climateChange.causes}. هذه مشكلة عالمية تتطلب تضافر جهود الجميع لحلها.`;
      }
      return `🌡️ تغير المناخ قضية حرجة تؤثر على كوكبنا. ${knowledgeBase.science.climateChange.effects}. لكن هناك حلول! ${knowledgeBase.science.climateChange.solutions}`;
    }

    // أسئلة التسجيل
    if (processedQuestion.includes('تسجيل') || processedQuestion.includes('اشتراك')) {
      return `📝 يمكنك التسجيل بسهولة من خلال موقعنا! اضغط على زر التسجيل وستجد جميع التفاصيل. التسجيل المبكر له مزايا خاصة!`;
    }

    // أسئلة الأوراق البحثية
    if (processedQuestion.includes('بحث') || processedQuestion.includes('ورقة') || processedQuestion.includes('paper')) {
      return `📚 مواعيد مهمة للباحثين:
      📅 آخر موعد للملخصات: ${knowledgeBase.conference.deadlines.abstract}
      📅 إشعار القبول: ${knowledgeBase.conference.deadlines.notification}
      📅 الأوراق النهائية: ${knowledgeBase.conference.deadlines.fullPaper}
      لا تفوت هذه المواعيد!`;
    }

    // أسئلة عامة ذكية
    if (processedQuestion.includes('تكنولوجيا') || processedQuestion.includes('technology')) {
      return `💻 ${knowledgeBase.general.technology}. في مؤتمرنا ستتعرف على أحدث التقنيات في مجال الطاقة!`;
    }

    if (processedQuestion.includes('تعليم') || processedQuestion.includes('education')) {
      return `🎓 ${knowledgeBase.general.education}. مؤتمرنا فرصة تعليمية رائعة للتعلم من الخبراء العالميين!`;
    }

    // ردود ذكية للتحيات
    if (processedQuestion.includes('مرحبا') || processedQuestion.includes('hello') || processedQuestion.includes('hi')) {
      const greetings = [
        '👋 أهلاً وسهلاً! سعيد جداً بلقائك!',
        '🌟 مرحباً بك! كيف يمكنني مساعدتك اليوم؟',
        '😊 أهلاً! أنا هنا لأجيب على جميع أسئلتك!'
      ];
      return greetings[Math.floor(Math.random() * greetings.length)];
    }

    if (processedQuestion.includes('شكرا') || processedQuestion.includes('thanks')) {
      return '🙏 العفو! سعيد جداً لمساعدتك. هل تحتاج لأي معلومات أخرى؟';
    }

    // رد ذكي للأسئلة غير المفهومة
    return `🤔 سؤال مثير للاهتمام! دعني أفكر... يمكنني مساعدتك في:
    🎯 معلومات المؤتمر (التاريخ، المكان، المواضيع)
    📝 التسجيل والاشتراك
    📚 الأوراق البحثية والمواعيد
    ⚡ الطاقة المتجددة والتكنولوجيا
    🌍 تغير المناخ والبيئة
    💡 أي موضوع علمي أو تقني آخر!

    أعد صياغة سؤالك وسأعطيك إجابة مفصلة! 😊`;
  };

  const findAnswer = (question) => {
    // حفظ السؤال في تاريخ المحادثة للتعلم
    setConversationHistory(prev => [...prev, question]);

    // تحليل السؤال
    const analysis = analyzeQuestion(question);

    // توليد رد ذكي
    const response = generateIntelligentResponse(analysis);

    // تحديث بيانات التعلم
    setLearningData(prev => ({
      ...prev,
      [question]: response,
      totalQuestions: (prev.totalQuestions || 0) + 1
    }));

    return response;
  };

  // 💡 اقتراحات ذكية للأسئلة
  const suggestQuestions = () => {
    const suggestions = [
      '📅 متى موعد المؤتمر؟',
      '📍 أين سيقام المؤتمر؟',
      '📝 كيف أسجل في المؤتمر؟',
      '⚡ ما هي الطاقة المتجددة؟',
      '🌍 ما أسباب تغير المناخ؟',
      '📚 متى آخر موعد للأوراق البحثية؟',
      '🎯 ما مواضيع المؤتمر؟',
      '🏆 هل سأحصل على شهادة؟'
    ];
    return suggestions[Math.floor(Math.random() * suggestions.length)];
  };

  const handleSendMessage = () => {
    if (!inputText.trim()) return;

    const userMessage = { type: 'user', text: inputText };
    setMessages(prev => [...prev, userMessage]);

    // تحديث ملف المستخدم
    setUserProfile(prev => ({
      ...prev,
      totalQuestions: (prev.totalQuestions || 0) + 1,
      lastQuestion: inputText,
      timestamp: new Date().toISOString()
    }));

    setInputText('');
    setIsTyping(true);

    // محاكاة تأخير الرد مع ذكاء متقدم
    setTimeout(() => {
      const botResponse = findAnswer(inputText);
      const suggestion = suggestQuestions();

      const enhancedResponse = `${botResponse}\n\n💡 اقتراح: ${suggestion}`;

      const botMessage = { type: 'bot', text: enhancedResponse };
      setMessages(prev => [...prev, botMessage]);
      setIsTyping(false);
    }, Math.random() * 1500 + 500); // تأخير عشوائي لمحاكاة التفكير
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      handleSendMessage();
    }
  };

  const toggleChat = () => {
    setShowChat(!showChat);
  };

  return (
    <>
      {/* زر ثابت في الركن السفلي الأيمن */}
      <button
        style={{
          position: 'fixed',
          bottom: '20px',
          right: '20px',
          zIndex: 1000,
          padding: '15px 20px',
          background: 'linear-gradient(45deg, #007bff, #0056b3)',
          color: 'white',
          border: 'none',
          borderRadius: '50px',
          cursor: 'pointer',
          boxShadow: '0 4px 15px rgba(0,123,255,0.3)',
          fontSize: '16px',
          fontWeight: 'bold',
          transition: 'all 0.3s ease',
          transform: showChat ? 'scale(0.9)' : 'scale(1)',
        }}
        onClick={toggleChat}
        onMouseEnter={(e) => {
          e.target.style.transform = 'scale(1.1)';
          e.target.style.boxShadow = '0 6px 20px rgba(0,123,255,0.4)';
        }}
        onMouseLeave={(e) => {
          e.target.style.transform = showChat ? 'scale(0.9)' : 'scale(1)';
          e.target.style.boxShadow = '0 4px 15px rgba(0,123,255,0.3)';
        }}
      >
        {showChat ? '✕' : '💬 Chat'}
      </button>

      {/* الصفحة اللي بتظهر عند الضغط */}
      {showChat && (
        <div
          style={{
            position: 'fixed',
            bottom: '70px',
            right: '20px',
            width: '300px',
            height: '400px',
            backgroundColor: 'white',
            boxShadow: '0 4px 8px rgba(0,0,0,0.2)',
            borderRadius: '10px',
            zIndex: 999,
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          {/* رأس الـ Chatbot */}
          <div
            style={{
              backgroundColor: '#007bff',
              color: 'white',
              padding: '10px',
              borderTopLeftRadius: '10px',
              borderTopRightRadius: '10px',
            }}
          >
            <h4 style={{ margin: 0 }}>محادثة الدعم</h4>
          </div>
          
          {/* محتوى الـ Chatbot */}
          <div style={{ flex: 1, padding: '10px', overflowY: 'auto', display: 'flex', flexDirection: 'column' }}>
            {/* الرسائل */}
            <div style={{ flex: 1, overflowY: 'auto', marginBottom: '10px' }}>
              {messages.map((message, index) => (
                <div
                  key={index}
                  style={{
                    marginBottom: '10px',
                    display: 'flex',
                    justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start'
                  }}
                >
                  <div
                    style={{
                      maxWidth: '85%',
                      padding: '10px 15px',
                      borderRadius: '18px',
                      backgroundColor: message.type === 'user'
                        ? 'linear-gradient(45deg, #007bff, #0056b3)'
                        : 'linear-gradient(45deg, #f8f9fa, #e9ecef)',
                      color: message.type === 'user' ? 'white' : '#333',
                      fontSize: '14px',
                      lineHeight: '1.5',
                      boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                      border: message.type === 'bot' ? '1px solid #dee2e6' : 'none',
                      whiteSpace: 'pre-line'
                    }}
                  >
                    {message.text}
                  </div>
                </div>
              ))}
              {isTyping && (
                <div style={{ display: 'flex', justifyContent: 'flex-start', marginBottom: '10px' }}>
                  <div
                    style={{
                      padding: '10px 15px',
                      borderRadius: '18px',
                      backgroundColor: 'linear-gradient(45deg, #f8f9fa, #e9ecef)',
                      fontSize: '14px',
                      border: '1px solid #dee2e6',
                      boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                      animation: 'pulse 1.5s infinite'
                    }}
                  >
                    🤖 أفكر في إجابة ذكية...
                  </div>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>

            {/* مربع الإدخال */}
            <div style={{ display: 'flex', gap: '5px' }}>
              <input
                type="text"
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="اكتب رسالتك هنا..."
                style={{
                  flex: 1,
                  padding: '8px',
                  border: '1px solid #ddd',
                  borderRadius: '20px',
                  outline: 'none',
                  fontSize: '14px'
                }}
              />
              <button
                onClick={handleSendMessage}
                style={{
                  padding: '8px 15px',
                  backgroundColor: '#007bff',
                  color: 'white',
                  border: 'none',
                  borderRadius: '20px',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
              >
                إرسال
              </button>
            </div>
          </div>

          {/* زر إغلاق */}
          <button
            style={{
              position: 'absolute',
              top: '5px',
              right: '10px',
              background: 'transparent',
              border: 'none',
              fontSize: '20px',
              cursor: 'pointer',
            }}
            onClick={toggleChat}
          >
            &times;
          </button>
        </div>
      )}
    </>
  );
};

export default ChatbotPopup;