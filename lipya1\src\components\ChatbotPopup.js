import React, { useState } from 'react';

const ChatbotPopup = () => {
  const [showChat, setShowChat] = useState(false);

  const toggleChat = () => {
    setShowChat(!showChat);
  };

  return (
    <>
      {/* زر ثابت في الركن السفلي الأيمن */}
      <button
        style={{
          position: 'fixed',
          bottom: '20px',
          right: '20px',
          zIndex: 1000,
          padding: '10px 20px',
          backgroundColor: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '50px',
          cursor: 'pointer',
        }}
        onClick={toggleChat}
      >
        Chat
      </button>

      {/* الصفحة اللي بتظهر عند الضغط */}
      {showChat && (
        <div
          style={{
            position: 'fixed',
            bottom: '70px',
            right: '20px',
            width: '300px',
            height: '400px',
            backgroundColor: 'white',
            boxShadow: '0 4px 8px rgba(0,0,0,0.2)',
            borderRadius: '10px',
            zIndex: 999,
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          {/* رأس الـ Chatbot */}
          <div
            style={{
              backgroundColor: '#007bff',
              color: 'white',
              padding: '10px',
              borderTopLeftRadius: '10px',
              borderTopRightRadius: '10px',
            }}
          >
            <h4 style={{ margin: 0 }}>محادثة الدعم</h4>
          </div>
          
          {/* محتوى الـ Chatbot */}
          <div style={{ flex: 1, padding: '10px', overflowY: 'auto' }}>
            {/* هنا تضع الـ iframe أو مكون الـ Chatbot الخاص بك */}
            {/* مثال: */}
            <iframe
              src="YOUR_CHATBOT_URL"
              title="Chatbot"
              style={{ width: '100%', height: '100%', border: 'none' }}
            />
          </div>

          {/* زر إغلاق */}
          <button
            style={{
              position: 'absolute',
              top: '5px',
              right: '10px',
              background: 'transparent',
              border: 'none',
              fontSize: '20px',
              cursor: 'pointer',
            }}
            onClick={toggleChat}
          >
            &times;
          </button>
        </div>
      )}
    </>
  );
};

export default ChatbotPopup;