"use client";

import React, { useState, useRef, useEffect } from 'react';

const ChatbotPopup = () => {
  const [showChat, setShowChat] = useState(false);
  const [messages, setMessages] = useState([
    { type: 'bot', text: 'مرحباً! أنا مساعد المؤتمر الذكي. كيف يمكنني مساعدتك؟' }
  ]);
  const [inputText, setInputText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // قاعدة بيانات الأسئلة والأجوبة
  const qaDatabase = {
    'مؤتمر': 'مؤتمر الطاقة المتجددة والنفط والغاز وتغير المناخ سيقام في طرابلس، ليبيا في نوفمبر 2025',
    'تاريخ': 'المؤتمر سيقام من 25-27 نوفمبر 2025',
    'موعد': 'المؤتمر سيقام من 25-27 نوفمبر 2025',
    'مكان': 'سيقام المؤتمر في طرابلس، ليبيا',
    'طرابلس': 'نعم، المؤتمر سيقام في طرابلس، ليبيا',
    'ليبيا': 'نعم، المؤتمر سيقام في طرابلس، ليبيا',
    'تسجيل': 'يمكنك التسجيل من خلال الضغط على زر التسجيل في الموقع أو زيارة الرابط المخصص',
    'اشتراك': 'يمكنك التسجيل من خلال الضغط على زر التسجيل في الموقع',
    'أوراق': 'آخر موعد لتقديم الملخصات: 15 يونيو 2025، آخر موعد للأوراق النهائية: 1 أكتوبر 2025',
    'بحث': 'آخر موعد لتقديم الملخصات: 15 يونيو 2025، آخر موعد للأوراق النهائية: 1 أكتوبر 2025',
    'ملخص': 'آخر موعد لتقديم الملخصات: 15 يونيو 2025',
    'رسوم': 'ستجد تفاصيل الرسوم في صفحة التسجيل',
    'سعر': 'ستجد تفاصيل الرسوم في صفحة التسجيل',
    'تكلفة': 'ستجد تفاصيل الرسوم في صفحة التسجيل',
    'إقامة': 'سنوفر معلومات عن الفنادق والإقامة قريباً',
    'فندق': 'سنوفر معلومات عن الفنادق والإقامة قريباً',
    'نقل': 'سيتم توفير معلومات النقل والمواصلات قريباً',
    'مواصلات': 'سيتم توفير معلومات النقل والمواصلات قريباً',
    'شهادة': 'نعم، سيحصل جميع المشاركين على شهادات مشاركة',
    'شهادات': 'نعم، سيحصل جميع المشاركين على شهادات مشاركة',
    'لغة': 'المؤتمر سيكون باللغتين العربية والإنجليزية',
    'عربي': 'المؤتمر سيكون باللغتين العربية والإنجليزية',
    'انجليزي': 'المؤتمر سيكون باللغتين العربية والإنجليزية',
    'مواضيع': 'المواضيع تشمل: الطاقة المتجددة، النفط والغاز، تغير المناخ، التنمية المستدامة',
    'طاقة': 'المؤتمر يركز على الطاقة المتجددة والطاقة الشمسية وطاقة الرياح',
    'نفط': 'المؤتمر يغطي مواضيع النفط والغاز والانتقال للطاقة النظيفة',
    'غاز': 'المؤتمر يغطي مواضيع النفط والغاز والانتقال للطاقة النظيفة',
    'مناخ': 'المؤتمر يناقش تغير المناخ والحلول البيئية',
    'بيئة': 'المؤتمر يناقش القضايا البيئية والتنمية المستدامة',
    'متحدثين': 'سيشارك خبراء وباحثون وصناع قرار من جميع أنحاء العالم',
    'خبراء': 'سيشارك خبراء وباحثون وصناع قرار من جميع أنحاء العالم',
    'معارض': 'نعم، سيكون هناك معرض مصاحب للمؤتمر',
    'معرض': 'نعم، سيكون هناك معرض مصاحب للمؤتمر',
    'ورش': 'سيتم تنظيم ورش عمل متخصصة خلال المؤتمر',
    'ورشة': 'سيتم تنظيم ورش عمل متخصصة خلال المؤتمر',
    'مرحبا': 'مرحباً بك! كيف يمكنني مساعدتك بخصوص المؤتمر؟',
    'شكرا': 'عفواً! سعيد لمساعدتك. هل لديك أسئلة أخرى؟',
    'مساعدة': 'يمكنني مساعدتك في أي استفسار حول المؤتمر. اسأل عن أي شيء!',
    'معلومات': 'يمكنني تقديم معلومات عن التاريخ، المكان، التسجيل، الأوراق، والمزيد'
  };

  const findAnswer = (question) => {
    const lowerQuestion = question.toLowerCase();
    for (const [keyword, answer] of Object.entries(qaDatabase)) {
      if (lowerQuestion.includes(keyword)) {
        return answer;
      }
    }
    return 'عذراً، لم أفهم سؤالك. يمكنك السؤال عن: المؤتمر، التاريخ، المكان، التسجيل، الأوراق، الرسوم، الإقامة، النقل، الشهادات، اللغة، المواضيع، المتحدثين، المعارض، ورش العمل';
  };

  const handleSendMessage = () => {
    if (!inputText.trim()) return;

    const userMessage = { type: 'user', text: inputText };
    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsTyping(true);

    // محاكاة تأخير الرد
    setTimeout(() => {
      const botResponse = findAnswer(inputText);
      const botMessage = { type: 'bot', text: botResponse };
      setMessages(prev => [...prev, botMessage]);
      setIsTyping(false);
    }, 1000);
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      handleSendMessage();
    }
  };

  const toggleChat = () => {
    setShowChat(!showChat);
  };

  return (
    <>
      {/* زر ثابت في الركن السفلي الأيمن */}
      <button
        style={{
          position: 'fixed',
          bottom: '20px',
          right: '20px',
          zIndex: 1000,
          padding: '15px 20px',
          background: 'linear-gradient(45deg, #007bff, #0056b3)',
          color: 'white',
          border: 'none',
          borderRadius: '50px',
          cursor: 'pointer',
          boxShadow: '0 4px 15px rgba(0,123,255,0.3)',
          fontSize: '16px',
          fontWeight: 'bold',
          transition: 'all 0.3s ease',
          transform: showChat ? 'scale(0.9)' : 'scale(1)',
        }}
        onClick={toggleChat}
        onMouseEnter={(e) => {
          e.target.style.transform = 'scale(1.1)';
          e.target.style.boxShadow = '0 6px 20px rgba(0,123,255,0.4)';
        }}
        onMouseLeave={(e) => {
          e.target.style.transform = showChat ? 'scale(0.9)' : 'scale(1)';
          e.target.style.boxShadow = '0 4px 15px rgba(0,123,255,0.3)';
        }}
      >
        {showChat ? '✕' : '💬 Chat'}
      </button>

      {/* الصفحة اللي بتظهر عند الضغط */}
      {showChat && (
        <div
          style={{
            position: 'fixed',
            bottom: '70px',
            right: '20px',
            width: '300px',
            height: '400px',
            backgroundColor: 'white',
            boxShadow: '0 4px 8px rgba(0,0,0,0.2)',
            borderRadius: '10px',
            zIndex: 999,
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          {/* رأس الـ Chatbot */}
          <div
            style={{
              backgroundColor: '#007bff',
              color: 'white',
              padding: '10px',
              borderTopLeftRadius: '10px',
              borderTopRightRadius: '10px',
            }}
          >
            <h4 style={{ margin: 0 }}>محادثة الدعم</h4>
          </div>
          
          {/* محتوى الـ Chatbot */}
          <div style={{ flex: 1, padding: '10px', overflowY: 'auto', display: 'flex', flexDirection: 'column' }}>
            {/* الرسائل */}
            <div style={{ flex: 1, overflowY: 'auto', marginBottom: '10px' }}>
              {messages.map((message, index) => (
                <div
                  key={index}
                  style={{
                    marginBottom: '10px',
                    display: 'flex',
                    justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start'
                  }}
                >
                  <div
                    style={{
                      maxWidth: '80%',
                      padding: '8px 12px',
                      borderRadius: '15px',
                      backgroundColor: message.type === 'user' ? '#007bff' : '#f1f1f1',
                      color: message.type === 'user' ? 'white' : 'black',
                      fontSize: '14px',
                      lineHeight: '1.4'
                    }}
                  >
                    {message.text}
                  </div>
                </div>
              ))}
              {isTyping && (
                <div style={{ display: 'flex', justifyContent: 'flex-start', marginBottom: '10px' }}>
                  <div
                    style={{
                      padding: '8px 12px',
                      borderRadius: '15px',
                      backgroundColor: '#f1f1f1',
                      fontSize: '14px'
                    }}
                  >
                    يكتب...
                  </div>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>

            {/* مربع الإدخال */}
            <div style={{ display: 'flex', gap: '5px' }}>
              <input
                type="text"
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="اكتب رسالتك هنا..."
                style={{
                  flex: 1,
                  padding: '8px',
                  border: '1px solid #ddd',
                  borderRadius: '20px',
                  outline: 'none',
                  fontSize: '14px'
                }}
              />
              <button
                onClick={handleSendMessage}
                style={{
                  padding: '8px 15px',
                  backgroundColor: '#007bff',
                  color: 'white',
                  border: 'none',
                  borderRadius: '20px',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
              >
                إرسال
              </button>
            </div>
          </div>

          {/* زر إغلاق */}
          <button
            style={{
              position: 'absolute',
              top: '5px',
              right: '10px',
              background: 'transparent',
              border: 'none',
              fontSize: '20px',
              cursor: 'pointer',
            }}
            onClick={toggleChat}
          >
            &times;
          </button>
        </div>
      )}
    </>
  );
};

export default ChatbotPopup;