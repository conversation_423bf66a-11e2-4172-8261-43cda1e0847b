{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/context/UserContext.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { createContext, useContext, useState, ReactNode } from \"react\";\r\n\r\ntype User = {\r\n  name?: string;\r\n  email?: string;\r\n  photoURL?: string;\r\n};\r\n\r\ntype UserContextType = {\r\n  user: User | null;\r\n  setUser: (user: User | null) => void;\r\n};\r\n\r\nconst UserContext = createContext<UserContextType | undefined>(undefined);\r\n\r\nexport function UserProvider({ children }: { children: ReactNode }) {\r\n  const [user, setUser] = useState<User | null>(null);\r\n\r\n  return (\r\n    <UserContext.Provider value={{ user, setUser }}>\r\n      {children}\r\n    </UserContext.Provider>\r\n  );\r\n}\r\n\r\nexport function useUser() {\r\n  const context = useContext(UserContext);\r\n  if (!context) {\r\n    throw new Error(\"useUser must be used within a UserProvider\");\r\n  }\r\n  return context;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAeA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAA2B;;IAChE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAE9C,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;YAAE;YAAM;QAAQ;kBAC1C;;;;;;AAGP;GARgB;KAAA;AAUT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/top/lipya%20project/lipya1/src/components/Navbar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport Image from \"next/image\";\r\nimport { Menu, X } from \"lucide-react\";\r\nimport { useUser } from \"../context/UserContext\"; // عدل حسب مكان الملف\r\nimport { useRouter } from \"next/navigation\";\r\n\r\n function Navbar() {\r\n  const [menuOpen, setMenuOpen] = useState(false);\r\n  const [dropdownOpen, setDropdownOpen] = useState(false);\r\n  const toggleMenu = () => setMenuOpen(!menuOpen);\r\n\r\n  const { user, logout } = useUser();\r\n  const router = useRouter();\r\n\r\n  const dropdownRef = useRef<HTMLDivElement>(null);\r\n\r\n  // إغلاق الـ dropdown عند الضغط خارجها\r\n  useEffect(() => {\r\n    function handleClickOutside(event: MouseEvent) {\r\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\r\n        setDropdownOpen(false);\r\n      }\r\n    }\r\n    if (dropdownOpen) {\r\n      document.addEventListener(\"mousedown\", handleClickOutside);\r\n    } else {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    }\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, [dropdownOpen]);\r\n\r\n  const handleLogout = async () => {\r\n    try {\r\n      await logout();\r\n      setDropdownOpen(false);\r\n      router.push(\"/login\");\r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n      // Even if logout fails, redirect to login\r\n      setDropdownOpen(false);\r\n      router.push(\"/login\");\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <nav className=\"absolute top-[35px] left-0 w-full bg-gray-800/30 text-white z-40 backdrop-blur-md border-b border-white/10\">\r\n  <div className=\"max-w-7xl mx-auto px-4 py-3 flex items-center justify-between h-16\">\r\n    {/* الشعار */}\r\n    <div className=\"flex items-center space-x-2 min-w-[60px]\">\r\n      <Image src=\"/logo.png\" alt=\"Logo\" width={50} height={40} />\r\n    </div>\r\n\r\n    {/* روابط سطح المكتب */}\r\n<ul className=\"hidden md:flex items-center gap-3  text-sm uppercase font-medium\">\r\n\r\n  <li>\r\n    <Link href=\"/\" className=\"hover:text-orange-400 transition\">\r\n      Home\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/about\" className=\"hover:text-orange-400 transition\">\r\n      About\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/committees\" className=\"hover:text-orange-400 transition\">\r\n      Committees\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/call-for-papers\" className=\"hover:text-orange-400 transition\">\r\n      Call  Papers\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/submission\" className=\"hover:text-orange-400 transition\">\r\n      Submission\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/registration\" className=\"hover:text-orange-400 transition\">\r\n      Registration\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/venue\" className=\"hover:text-orange-400 transition\">\r\n      Venue\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/expo\" className=\"hover:text-orange-400 transition\">\r\n     Expo\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link href=\"/the-best\" className=\"hover:text-orange-400 transition\">\r\n        Best\r\n    </Link>\r\n  </li>\r\n\r\n  <li>\r\n    <Link\r\n      href=\"/contact-us\"\r\n      scroll={true}\r\n      onClick={toggleMenu}\r\n      className=\"bg-orange-500 hover:bg-orange-600 text-white px-4 py-1.5 rounded-md text-xs font-semibold\"\r\n    >\r\n      Contact \r\n    </Link>\r\n  </li>\r\n</ul>\r\n\r\n\r\n    {/* صورة المستخدم أو زر تسجيل الدخول + زر القائمة للموبايل */}\r\n    <div className=\"flex items-center space-x-3 min-w-[100px] justify-end relative\">\r\n      {user ? (\r\n        <>\r\n          <button onClick={() => setDropdownOpen(!dropdownOpen)} className=\"focus:outline-none\">\r\n            <Image\r\n              src={user.photoURL || \"/avatar.png\"}\r\n              alt=\"User\"\r\n              width={36}\r\n              height={36}\r\n              className=\"rounded-full border border-white\"\r\n            />\r\n          </button>\r\n\r\n          {dropdownOpen && (\r\n            <div\r\n              ref={dropdownRef}\r\n              className=\"absolute right-0 mt-2 w-40 bg-gray-900 border border-gray-700 rounded shadow-lg z-50\"\r\n            >\r\n              <button\r\n                onClick={handleLogout}\r\n                className=\"block w-full text-left px-4 py-2 text-sm hover:bg-orange-600 hover:text-white text-red-500\"\r\n              >\r\n                تسجيل الخروج\r\n              </button>\r\n            </div>\r\n          )}\r\n        </>\r\n      ) : (\r\n        <Link\r\n          href=\"/login\"\r\n          className=\"text-sm bg-orange-500 hover:bg-orange-600 px-3 py-1 rounded text-white\"\r\n        >\r\n          Login\r\n        </Link>\r\n      )}\r\n\r\n      {/* زر القائمة للموبايل */}\r\n      <button\r\n        className=\"md:hidden text-white\"\r\n        onClick={toggleMenu}\r\n        aria-label=\"Toggle menu\"\r\n      >\r\n        {menuOpen ? <X size={28} /> : <Menu size={28} />}\r\n      </button>\r\n    </div>\r\n  </div>\r\n\r\n  {/* القائمة الجانبية للموبايل */}\r\n  {menuOpen && (\r\n    <div className=\"md:hidden flex flex-col px-6 pb-4 pt-2 space-y-4 bg-gray-800/30 text-white backdrop-blur-md border-t border-white/10\">\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/\" onClick={toggleMenu}>Home</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/about\" onClick={toggleMenu}>About us</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/committees\" onClick={toggleMenu}>Committees</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/call-for-papers\" onClick={toggleMenu}>Call For Papers</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/submission\" onClick={toggleMenu}>Submission</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/registration\" onClick={toggleMenu}>Registration</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/venue\" onClick={toggleMenu}>Venue</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/expo\">IREGO Expo</Link>{/* Corrected href */}\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/the-best\">IREGO The Best</Link>\r\n    \r\n      \r\n      <Link\r\n        href=\"/contact-us\"\r\n        scroll={true}\r\n        onClick={toggleMenu}\r\n        className=\"block bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md text-sm font-semibold w-fit\"\r\n      >\r\n        Contact Us\r\n      </Link>\r\n    </div>\r\n  )}\r\n</nav>\r\n    \r\n    </>\r\n  );\r\n}\r\nexport default Navbar ; "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA,oOAAkD,qBAAqB;AACvE;;;AAPA;;;;;;;AASC,SAAS;;IACR,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,aAAa,IAAM,YAAY,CAAC;IAEtC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,SAAS,mBAAmB,KAAiB;gBAC3C,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;oBAC9E,gBAAgB;gBAClB;YACF;YACA,IAAI,cAAc;gBAChB,SAAS,gBAAgB,CAAC,aAAa;YACzC,OAAO;gBACL,SAAS,mBAAmB,CAAC,aAAa;YAC5C;YACA;oCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;2BAAG;QAAC;KAAa;IAEjB,MAAM,eAAe;QACnB,IAAI;YACF,MAAM;YACN,gBAAgB;YAChB,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,0CAA0C;YAC1C,gBAAgB;YAChB,OAAO,IAAI,CAAC;QACd;IACF;IAEA,qBACE;kBACE,cAAA,6LAAC;YAAI,WAAU;;8BACnB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCAAC,KAAI;gCAAY,KAAI;gCAAO,OAAO;gCAAI,QAAQ;;;;;;;;;;;sCAI3D,6LAAC;4BAAG,WAAU;;8CAEZ,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAmC;;;;;;;;;;;8CAK9D,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAmC;;;;;;;;;;;8CAKnE,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAc,WAAU;kDAAmC;;;;;;;;;;;8CAKxE,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAmB,WAAU;kDAAmC;;;;;;;;;;;8CAK7E,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAc,WAAU;kDAAmC;;;;;;;;;;;8CAKxE,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAgB,WAAU;kDAAmC;;;;;;;;;;;8CAK1E,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAmC;;;;;;;;;;;8CAKnE,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAQ,WAAU;kDAAmC;;;;;;;;;;;8CAKlE,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAAmC;;;;;;;;;;;8CAKtE,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,QAAQ;wCACR,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAQD,6LAAC;4BAAI,WAAU;;gCACZ,qBACC;;sDACE,6LAAC;4CAAO,SAAS,IAAM,gBAAgB,CAAC;4CAAe,WAAU;sDAC/D,cAAA,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAK,KAAK,QAAQ,IAAI;gDACtB,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;wCAIb,8BACC,6LAAC;4CACC,KAAK;4CACL,WAAU;sDAEV,cAAA,6LAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;iEAOP,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAMH,6LAAC;oCACC,WAAU;oCACV,SAAS;oCACT,cAAW;8CAEV,yBAAW,6LAAC,+LAAA,CAAA,IAAC;wCAAC,MAAM;;;;;6DAAS,6LAAC,qMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;gBAM/C,0BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAI,SAAS;sCAAY;;;;;;sCACjF,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAS,SAAS;sCAAY;;;;;;sCACtF,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAc,SAAS;sCAAY;;;;;;sCAC3F,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAmB,SAAS;sCAAY;;;;;;sCAChG,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAc,SAAS;sCAAY;;;;;;sCAC3F,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAgB,SAAS;sCAAY;;;;;;sCAC7F,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAS,SAAS;sCAAY;;;;;;sCACtF,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;sCAAQ;;;;;;sCAChE,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;sCAAY;;;;;;sCAGpE,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,QAAQ;4BACR,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;AASP;GAnMU;;QAKiB,iIAAA,CAAA,UAAO;QACjB,qIAAA,CAAA,YAAS;;;KANhB;uCAoMK", "debugId": null}}]}