/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M10 7.5 8 10l2 2.5", key: "xb17xw" }],
  ["path", { d: "m14 7.5 2 2.5-2 2.5", key: "5rap1v" }],
  ["path", { d: "M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z", key: "1lielz" }]
];
const MessageSquareCode = createLucideIcon("message-square-code", __iconNode);

export { __iconNode, MessageSquareCode as default };
//# sourceMappingURL=message-square-code.js.map
