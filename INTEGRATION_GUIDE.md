# دليل ربط الباك إند بالفرونت إند

## نظرة عامة
تم ربط مشروع Laravel (iregoo) مع مشروع Next.js (lipya1) بنجاح.

## التغييرات المطبقة

### في Laravel (iregoo):
1. **إعداد CORS**: تم إنشاء ملف `config/cors.php` للسماح بطلبات من Next.js
2. **CORS Middleware**: تم إنشاء `app/Http/Middleware/CorsMiddleware.php` كبديل إضافي
3. **API Routes**: الـ API موجود في `routes/api.php` ويدعم:
   - Authentication (login, register, logout, me)
   - Conferences, Speakers, News, Sponsors, Events
   - Registration management

### في Next.js (lipya1):
1. **API Helper**: تم إنشاء `src/lib/api.ts` للتعامل مع جميع API calls
2. **User Context**: تم تحديث `src/context/UserContext.tsx` للتعامل مع JWT
3. **Authentication Pages**: تم تحديث صفحات Login و Signup للاتصال بالـ API الحقيقي
4. **Navbar**: تم تحديث Navbar للتعامل مع حالة المصادقة
5. **Environment Variables**: تم إنشاء `.env.local` لتكوين API URL
6. **Next.js Config**: تم تحديث `next.config.ts` لإعداد proxy
7. **Middleware**: تم إنشاء `src/middleware.ts` لحماية الصفحات

## كيفية تشغيل المشروع

### 1. تشغيل Laravel (الباك إند):
```bash
cd iregoo
php artisan serve
```
سيعمل على: http://localhost:8000

### 2. تشغيل Next.js (الفرونت إند):
```bash
cd lipya1
npm run dev
```
سيعمل على: http://localhost:3000

## API Endpoints المتاحة

### Authentication:
- POST `/api/v1/auth/login` - تسجيل الدخول
- POST `/api/v1/auth/register` - إنشاء حساب جديد
- POST `/api/v1/auth/logout` - تسجيل الخروج
- GET `/api/v1/auth/me` - الحصول على بيانات المستخدم الحالي

### Public Data:
- GET `/api/v1/conferences` - جميع المؤتمرات
- GET `/api/v1/speakers` - جميع المتحدثين
- GET `/api/v1/news` - جميع الأخبار
- GET `/api/v1/sponsors` - جميع الرعاة
- GET `/api/v1/events` - جميع الفعاليات

## الميزات المطبقة

### 1. المصادقة (Authentication):
- تسجيل الدخول والخروج
- إنشاء حساب جديد
- حفظ JWT token في localStorage
- التحقق التلقائي من صحة الـ token

### 2. إدارة الحالة:
- UserContext للتعامل مع بيانات المستخدم
- Loading states أثناء العمليات
- Error handling للأخطاء

### 3. الأمان:
- CORS configuration
- JWT authentication
- Protected routes middleware

## ملاحظات مهمة

1. **متغيرات البيئة**: تأكد من تحديث `.env.local` في Next.js إذا تغير عنوان الـ API
2. **Database**: تأكد من إعداد قاعدة البيانات في Laravel
3. **JWT Secret**: تأكد من إعداد JWT_SECRET في Laravel
4. **CORS**: إذا واجهت مشاكل CORS، تأكد من تشغيل Laravel على المنفذ الصحيح

## اختبار الربط

1. شغل كلا المشروعين
2. اذهب إلى http://localhost:3000
3. جرب تسجيل حساب جديد
4. جرب تسجيل الدخول
5. تأكد من ظهور بيانات المستخدم في Navbar

## استكشاف الأخطاء

### إذا لم يعمل الربط:
1. تأكد من تشغيل Laravel على المنفذ 8000
2. تأكد من إعداد CORS بشكل صحيح
3. تحقق من console في المتصفح للأخطاء
4. تأكد من صحة API URL في `.env.local`

### أخطاء شائعة:
- **CORS Error**: تأكد من إعداد CORS في Laravel
- **401 Unauthorized**: تأكد من صحة JWT token
- **Network Error**: تأكد من تشغيل Laravel server
