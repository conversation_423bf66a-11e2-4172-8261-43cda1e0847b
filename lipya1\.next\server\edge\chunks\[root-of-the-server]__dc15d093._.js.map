{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport type { NextRequest } from 'next/server';\n\n// Define protected routes that require authentication\nconst protectedRoutes = [\n  '/dashboard',\n  '/profile',\n  '/admin',\n];\n\n// Define auth routes that should redirect to home if user is already logged in\nconst authRoutes = [\n  '/login',\n  '/signup',\n];\n\nexport function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl;\n  \n  // Check if user has auth token\n  const token = request.cookies.get('auth_token')?.value || \n                request.headers.get('authorization')?.replace('Bearer ', '');\n\n  // Check if the current path is protected\n  const isProtectedRoute = protectedRoutes.some(route => \n    pathname.startsWith(route)\n  );\n\n  // Check if the current path is an auth route\n  const isAuthRoute = authRoutes.some(route => \n    pathname.startsWith(route)\n  );\n\n  // If trying to access protected route without token, redirect to login\n  if (isProtectedRoute && !token) {\n    const loginUrl = new URL('/login', request.url);\n    loginUrl.searchParams.set('redirect', pathname);\n    return NextResponse.redirect(loginUrl);\n  }\n\n  // If trying to access auth routes with token, redirect to home\n  if (isAuthRoute && token) {\n    return NextResponse.redirect(new URL('/', request.url));\n  }\n\n  return NextResponse.next();\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */\n    '/((?!api|_next/static|_next/image|favicon.ico|.*\\\\.png$|.*\\\\.jpg$|.*\\\\.jpeg$|.*\\\\.gif$|.*\\\\.svg$).*)',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAGA,sDAAsD;AACtD,MAAM,kBAAkB;IACtB;IACA;IACA;CACD;AAED,+EAA+E;AAC/E,MAAM,aAAa;IACjB;IACA;CACD;AAEM,SAAS,WAAW,OAAoB;IAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,+BAA+B;IAC/B,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe,SACnC,QAAQ,OAAO,CAAC,GAAG,CAAC,kBAAkB,QAAQ,WAAW;IAEvE,yCAAyC;IACzC,MAAM,mBAAmB,gBAAgB,IAAI,CAAC,CAAA,QAC5C,SAAS,UAAU,CAAC;IAGtB,6CAA6C;IAC7C,MAAM,cAAc,WAAW,IAAI,CAAC,CAAA,QAClC,SAAS,UAAU,CAAC;IAGtB,uEAAuE;IACvE,IAAI,oBAAoB,CAAC,OAAO;QAC9B,MAAM,WAAW,IAAI,IAAI,UAAU,QAAQ,GAAG;QAC9C,SAAS,YAAY,CAAC,GAAG,CAAC,YAAY;QACtC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,+DAA+D;IAC/D,IAAI,eAAe,OAAO;QACxB,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,KAAK,QAAQ,GAAG;IACvD;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;;KAOC,GACD;KACD;AACH"}}]}